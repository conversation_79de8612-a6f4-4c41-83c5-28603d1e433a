{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "private": true, "description": "塞上早茶", "templateInfo": {"name": "react-NutUI", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:weapp:dev": "taro build --type weapp --env development", "dev:weapp": "npm run build:weapp -- --watch", "postinstall": "weapp-tw patch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@nutui/nutui-react-taro": "^3.0.6", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-html": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "immer": "^10.1.1", "markdown-it": "^14.1.0", "query-string": "^9.1.1", "react": "^18.0.0", "react-async-hook": "^4.0.0", "react-dom": "^18.0.0", "use-immer": "^0.11.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.0.9", "@tarojs/taro-loader": "4.0.9", "@tarojs/webpack5-runner": "4.0.9", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.0.9", "eslint": "^8.12.0", "eslint-config-taro": "4.0.9", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.5.3", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "tailwindcss": "3", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "weapp-tailwindcss": "^4.1.4", "webpack": "5.78.0"}}