import { Picker } from '@nutui/nutui-react-taro';
import { View, Text } from '@tarojs/components';
import { useState } from 'react';

interface Props {
    placeholder?: string;
    startTime: string;
    endTime: string;
    onChange?: (startTime: string, endTime: string) => void;
    title?: string;
}

// 生成0-23小时的选项
const generateHours = () => {
    const hours: any[] = [];
    for (let i = 0; i < 24; i++) {
        const hour = i.toString().padStart(2, '0');
        hours.push({
            label: hour,
            value: hour
        });
    }
    return hours;
};

const hours = generateHours();

export default function TimeRangePicker({ placeholder, startTime, endTime, onChange, title }: Props) {
    const [visible, setVisible] = useState(false);

    // 显示的文本
    const displayText = startTime && endTime ? `${startTime}点 至 ${endTime}点` : undefined;

    return (
        <>
            <View onClick={() => setVisible(true)}>
                {displayText || <Text className={'text-[#757575]'}>{placeholder}</Text>}
            </View>

            <Picker
                visible={visible}
                title={title || '选择时间范围'}
                options={[hours, [{ label: '至', value: '至' }], hours]}
                value={[startTime, '至', endTime]}
                onClose={() => setVisible(false)}
                onConfirm={(values) => {
                    // 确保结束时间大于开始时间
                    const start = values[0].value as string;
                    const end = values[2].value as string; // 第三列是结束时间

                    // 如果结束时间小于开始时间，自动调整为开始时间+2小时
                    let adjustedEnd = end;
                    if (parseInt(end) <= parseInt(start)) {
                        const newEnd = parseInt(start) + 2;
                        adjustedEnd = newEnd >= 24 ? (newEnd - 24).toString().padStart(2, '0')
                            : newEnd.toString().padStart(2, '0');
                    }
                    onChange?.(start, adjustedEnd);
                }}
            />
        </>
    );
}
