import { SafeArea } from '@nutui/nutui-react-taro';
import { Image, Input, InputProps, Navigator, ScrollView, Text, View } from '@tarojs/components';
import Taro, { getFileSystemManager, requirePlugin } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import Background from '../../components/background';
import Markdown from '../../components/markdown';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import useUser from '../../hooks/use-user';
import request from '../../utils/request';

declare module '@nutui/nutui-react-taro' {
    interface TaroInputProps {
        onConfirm?: InputProps['onConfirm'];
    }
}

const plugin = requirePlugin('QCloudAIVoice');
plugin.setQCloudSecret('1330938207', 'AKIDoW7bmocJvPn00tdAZmpiSqvvpmcXZR9g', 'PabP1c9V9lEYjFcqvreHk3CC2kLn6GG8', true);

const { platform } = Taro.getDeviceInfo();

export default function() {
    const user = useUser();

    if (!user) {
        return null;
    }

    const [loading, setLoading] = useState(false);
    const [recording, setRecording] = useState(false);
    const [conversation, setConversation] = useImmer<Conversation>({
        messages: []
    });

    useRequest({
        url: 'chat',
    }, {
        onSuccess({ conversation }) {
            if (conversation) {
                setConversation(conversation);
            }
        }
    });

    const { result: questions = [], refresh } = useRequest<string[]>({ url: 'chat/questions' });

    const [focus, setFocus] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [elementHeight, setElementHeight] = useState(0);
    const [scrollTop, setScrollTop] = useState(0);

    const scrollToBottom = () => {
        setTimeout(() => {
            const query = Taro.createSelectorQuery();
            query.select('#scrollView')
                 .boundingClientRect()
                 .exec(function([rect]) {
                     setScrollTop(rect.height - elementHeight);
                 });
        }, 200);
    };

    useEffect(() => {
        // 延迟一下，确保元素已经渲染
        setElementHeight(0);
        setTimeout(() => {
            const query = Taro.createSelectorQuery();
            query.select('#scrollArea')
                 .boundingClientRect()
                 .exec(function([rect]) {
                     setElementHeight(rect.height - 13);
                 });

            scrollToBottom();
        }, 500);
    }, [questions, conversation.messages.length === 0]);

    const record = () => {
        if (loading) {
            return;
        }
        const recorderManager = Taro.getRecorderManager();
        if (recording) {
            setRecording(false);
            recorderManager.stop();
        } else {
            setRecording(true);
            const format = platform === 'devtools' || platform === 'windows' ? 'aac' : 'mp3';

            recorderManager.onStop((res) => {
                const data = getFileSystemManager().readFileSync(res.tempFilePath, 'base64');
                plugin.sentenceRecognition({
                    engSerViceType: '16k_zh',  //引擎类型
                    sourceType: 1,  //1：语音数据
                    voiceFormat: format,
                    data: data,
                    dataLen: res.fileSize,
                    success: function({ result }) {
                        if (result) {
                            setInputValue(prev => prev + result);
                            setFocus(true);
                        }
                    },
                });
            });

            recorderManager.start({
                duration: 60000,
                numberOfChannels: 1,
                sampleRate: 16000,
                format: format,
            });
        }
    };

    const send = async (query: string) => {
        if (!loading && query) {
            try {
                setLoading(true);
                setConversation(draft => {
                    draft.messages.push({
                        query,
                        content: '',
                    });
                });

                // 发送消息后滚动到底部
                scrollToBottom();

                await request({
                    url: 'chat',
                    method: 'POST',
                    data: {
                        query,
                        conversation: conversation.id
                    },
                    onMessage(message) {
                        const content = JSON.parse(message);
                        setConversation(({ messages }) => {
                            const message = messages[messages.length - 1];
                            message.content += content;
                        });
                        //滚动到底部
                        scrollToBottom();
                    }
                });
            } finally {
                setLoading(false);
            }
        }
    };

    return <Background className={'h-screen flex flex-col'}>
        <NavBar>
            <PageTitle />
        </NavBar>
        <View className={'relative pt-[86px] mt-[12px] flex-1 flex flex-col'}>
            <Image className={'absolute top-[38px] left-[0px] right-[0px] w-auto z-[1]'} mode={'widthFix'} src={require('../../assets/chat.svg')} />
            <Image className={'w-[95px] h-[86px] absolute z-[2] top-[0px] left-[10px]'} src={require('../../assets/logo.png')} />
            <View className={'absolute z-[2] top-[51px] right-[31px] text-[18px] font-semibold'}><Text className={'bg-gradient-to-r from-[#7D173D] to-[#0A1E5F] bg-clip-text text-transparent'}>Hi～欢迎来到吴忠！</Text>👏</View>
            <View className={'flex-1 bg-[#F6F6F6] relative z-[2] mx-[-15px] rounded-[16px] flex flex-col'}>
                {conversation.messages.length === 0 && <>
                    {questions.length > 0 &&
                        <View className={'p-[16px_12px] bg-gradient-to-r from-[#DBFDF4] via-[#F8F1FC] via-49% to-[#ECFBFF] rounded-[16px] flex flex-col gap-[12px] border-solid border-[1px] border-[rgba(255,255,255,0.60)]'}>
                            <View className={'flex items-center justify-between'}>
                                <View className={'text-[15px] text-[#279BA9] font-semibold flex items-center gap-[4px]'}><Image className={'size-[18px]'} src={require('../../assets/help.svg')} />猜你想问</View>
                                <View onClick={refresh} className={'text-[13px] text-[#279BA9] font-semibold flex items-center gap-[4px]'}>
                                    <Image className={'size-[14px]'} src={require('../../assets/refresh.svg')} />换一批
                                </View>
                            </View>
                            <View className={'flex flex-col gap-[10px]'}>
                                {questions.map(question =>
                                    <View onClick={() => send(question)} className={'flex p-[8px_16px] justify-between items-center rounded-[30px] bg-[rgba(255,255,255,0.90)]'}>
                                        <View className={'text-[14px] text-[#454545]'}>{question}</View>
                                        <Image className={'size-[12px]'} src={require('../../assets/right.svg')} />
                                    </View>)}
                            </View>
                        </View>}
                    <View className={'flex gap-[8px] p-[15px]'}>
                        <Navigator url={'/pages/eatery/index'} className={'flex items-center gap-[4px] p-[4px_12px] bg-[rgba(255,255,255,0.90)] rounded-[12px] flex-1 text-[15px]'}>
                            <Image className={'size-[44px]'} src={require('../../assets/meishi.png')} />
                            美食
                        </Navigator>
                        <Navigator url={'/pages/spot/index'} className={'flex items-center gap-[4px] p-[4px_12px] bg-[rgba(255,255,255,0.90)] rounded-[12px] flex-1 text-[15px]'}>
                            <Image className={'size-[44px]'} src={require('../../assets/wanle.png')} />
                            玩乐
                        </Navigator>
                        <Navigator url={'/pages/hotel/index'} className={'flex items-center gap-[4px] p-[4px_12px] bg-[rgba(255,255,255,0.90)] rounded-[12px] flex-1 text-[15px]'}>
                            <Image className={'size-[44px]'} src={require('../../assets/zhusu.png')} />
                            住宿
                        </Navigator>
                    </View>
                </>}
                <View id='scrollArea' className={'flex-1 pt-[13px]'}>
                    <ScrollView
                        scrollY
                        style={{ height: elementHeight }}
                        scrollTop={scrollTop}
                        scrollWithAnimation
                    >
                        <View id='scrollView' className={'flex flex-col gap-[16px] px-[13px]'}>
                            {conversation.messages.map((message) => (
                                <>
                                    <View className={'flex justify-end'}>
                                        <View className={'p-[12px_16px] bg-[#58B4BF] rounded-[12px] text-white'}>{message.query}</View>
                                    </View>
                                    <View className={'flex justify-start'}>
                                        <Markdown className={'p-[12px_16px] bg-white rounded-[12px]'} content={message.content} />
                                    </View>
                                </>
                            ))}
                        </View>
                    </ScrollView>
                </View>
                <View className={'flex-shrink-0 p-[15px]'}>
                    <View className={'pe-[15px] bg-[#FFF] border border-[rgba(153,153,153,0.20)] rounded-[12px] flex items-center gap-[4px]'}>
                        <Input
                            focus={focus}
                            onBlur={() => {
                                setFocus(false);
                            }}
                            disabled={loading}
                            className={'flex-1 p-[13px_15px]'}
                            confirmType={'send'}
                            placeholder={'输入你的问题...'}
                            value={inputValue}
                            onInput={(e) => {
                                setInputValue(e.detail.value);
                            }}
                            cursorSpacing={20}
                            onConfirm={() => {
                                send(inputValue);
                                setInputValue('');
                            }} />
                        <View onClick={record} className={'flex items-center justify-center'}>
                            {recording ?
                                <Image className={'size-[24px]'} src={require('../../assets/recording.svg')} /> :
                                <Image className={'size-[24px]'} src={require('../../assets/voice.svg')} />}
                        </View>
                    </View>
                </View>
                <SafeArea position={'bottom'} />
            </View>
        </View>
    </Background>;
}
