@use "tailwindcss/base";
@use "tailwindcss/components";
@use "tailwindcss/utilities";

page {
    --nutui-color-primary: #58B4BF;
    --nutui-cell-group-background-color: none;
    --nutui-textarea-padding: 0;
    --nutui-input-padding: 0;
    --nutui-color-primary-light-pressed: #C5E8EC;
    --nutui-radio-button-background: rgba(88, 180, 191, 0.1);
    --nutui-tabs-titles-background-color: #FFF;
    --nutui-radiogroup-radio-margin: 0;
    --nutui-radiogroup-radio-margin-bottom: 0;
    --tw-border-style: solid;

    background: rgba(246, 246, 246, 1);
    font-family: "PingFang SC", -apple-system, system-ui, sans-serif;
    line-height: 1.5;
    color: #333333;

    .nut-popup {
        min-height: auto;
    }

    .nut-input-container {
        height: auto;
    }

    .nut-tabs-titles-item-active {
        .nut-tabs-titles-item-line {
            display: none;
        }

        .nut-tabs-titles-item-text {
            position: relative;

            &:after {
                content: " ";
                width: 100%;
                height: 2px;
                background: #58B4BF;
                bottom: 0;
                left: 0;
                right: 0;
                position: absolute;
            }
        }
    }
}

.shadow-custom {
    box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05)
}

.bg-gradient-FF724C {
    background: linear-gradient(180deg, #FF724C 0%, #FFFFFF 100%);
}

.mask-rb {
    mask: radial-gradient(circle at 0 0, transparent 20px, #000 0),
    radial-gradient(circle at 100% 0, transparent 20px, #000 0),
    radial-gradient(circle at 0 100%, transparent 20px, #000 0),
    radial-gradient(circle at 100% 100%, transparent 20px, #000 0);
    mask-size: 51% 51%;
    mask-repeat: no-repeat;
}
