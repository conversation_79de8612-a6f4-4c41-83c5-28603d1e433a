import { useDidShow } from '@tarojs/taro';
import { DependencyList, useCallback, useEffect } from 'react';
import { useAsyncCallback, UseAsyncCallbackOptions } from 'react-async-hook';
import request, { Option } from '../utils/request';

type UseRequestOptions<T> = {
    manual?: boolean
    refreshDeps?: DependencyList;
} & UseAsyncCallbackOptions<T>;

export default function useRequest<T = any>(option: Option, {
    manual,
    refreshDeps,
    ...options
}: UseRequestOptions<T> = {}) {
    let { execute, currentParams, error, ...others } = useAsyncCallback(async (params?: Partial<Option>) => {
        return await request({
            ...option,
            ...params
        });
    }, {
        setLoading(state) {
            return {
                ...state,
                status: 'loading',
                loading: true,
                error: undefined
            };
        },
        ...options
    });

    useEffect(() => {
        if (!manual && !refreshDeps) {
            try {
                execute();
            } catch {
            }
        }
    }, []);

    useDidShow(() => {
        if (!manual && !refreshDeps && others.status !== 'not-requested') {
            try {
                execute();
            } catch {
            }
        }
    });

    useEffect(() => {
        if (refreshDeps) {
            refresh();
        }
    }, refreshDeps);

    const refresh = useCallback(async () => {
        try {
            if (currentParams) {
                await execute(...currentParams);
            } else {
                await execute();
            }
        } catch {

        }
    }, [execute, currentParams]);

    return {
        refresh,
        execute,
        error,
        ...others
    };
}
