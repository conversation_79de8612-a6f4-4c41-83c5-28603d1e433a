import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import { UnifiedWebpackPluginV5 } from 'weapp-tailwindcss/webpack';
import devConfig from './dev';
import prodConfig from './prod';
// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'webpack5'>(async (merge) => {
    const baseConfig: UserConfigExport<'webpack5'> = {
        projectName: 'saishangzaocha',
        date: '2025-3-26',
        designWidth: 375,
        deviceRatio: {
            640: 2.34 / 2,
            750: 1,
            375: 2,
            828: 1.81 / 2
        },
        sourceRoot: 'src',
        outputRoot: 'dist',
        plugins: ['@tarojs/plugin-html'],
        defineConstants: {},
        copy: {
            patterns: [],
            options: {}
        },
        framework: 'react',
        compiler: {
            type: 'webpack5',
            prebundle: {
                enable: false
            }
        },
        cache: {
            enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
        },
        mini: {
            postcss: {
                pxtransform: {
                    enable: true,
                    config: {
                        selectorBlackList: ['nut-']
                    }
                },
                url: {
                    enable: true,
                    config: {
                        limit: 1024 // 设定转换尺寸上限
                    }
                },
                cssModules: {
                    enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
                    config: {
                        namingPattern: 'module', // 转换模式，取值为 global/module
                        generateScopedName: '[name]__[local]___[hash:base64:5]'
                    }
                }
            },
            webpackChain(chain) {
                chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin);
                chain.merge({
                    plugin: {
                        install: {
                            plugin: UnifiedWebpackPluginV5,
                            args: [{
                                appType: 'taro',
                                rem2rpx: true,
                                injectAdditionalCssVarScope: true
                            }]
                        }
                    }
                });
            }
        },
    };
    if (process.env.NODE_ENV === 'development') {
        // 本地开发构建配置（不混淆压缩）
        return merge({}, baseConfig, devConfig);
    }
    // 生产构建配置（默认开启压缩混淆等）
    return merge({}, baseConfig, prodConfig);
});
