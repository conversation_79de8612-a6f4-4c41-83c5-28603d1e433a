import {Input, Radio, RadioGroup, SafeArea, TextArea} from '@nutui/nutui-react-taro';
import {Image, Text, View} from '@tarojs/components';
import Taro, {useRouter} from '@tarojs/taro';
import {useState} from 'react';
import {useImmer} from 'use-immer';
import Background from '../../components/background';
import TimePicker from '../../components/time-picker';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import useUser from '../../hooks/use-user';
import getImageUrl from '../../utils/get-image-url';
import request from '../../utils/request';

export default function Create() {
    const user = useUser();

    if (!user) {
        return null;
    }

    const {params = {}} = useRouter();
    const [isEdit, setIsEdit] = useState(false);

    // 初始化空数据
    const [data, setData] = useImmer<Omit<Eatery, 'id'>>({
        type: 1,
        images: [],
        name: '',
        tags: [],
        hours: {
            start: '',
            end: ''
        },
        address: '',
        phone: '',
        location: undefined,
        cost: '',
        can_reserve: 0
    });

    // 获取店铺信息，如果存在则进入编辑模式
    useRequest(
        {url: 'user/eatery'},
        {
            onSuccess: (eatery) => {
                if (eatery) {
                    setIsEdit(true);
                    setData({
                        type: eatery.type || 1,
                        images: eatery.images || [],
                        name: eatery.name || '',
                        tags: eatery.tags || [],
                        hours: eatery.hours || {start: '', end: ''},
                        address: eatery.address || '',
                        phone: eatery.phone || '',
                        location: eatery.location,
                        cost: eatery.cost || '',
                        can_reserve: eatery.can_reserve || 0
                    });
                }
            },
            refreshDeps: []
        }
    );

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'店铺信息管理'}/>
            </NavBar>
        </Background>
        <View className={'p-[12px] pb-[70px]'}>
            <View className={'flex flex-col gap-[12px]'}>
                <View className={'px-[12px] py-[16px] rounded-xl flex flex-col gap-[16px] bg-white'}>
                    <View className={'text-[15px] flex flex-row justify-between items-center'}>
                        <Text className={'font-semibold'}>店铺图片</Text>
                        <Text className={'text-[13px] text-[#999999]'}>(最多可上传9张)</Text>
                    </View>
                    <View className={'grid grid-cols-3 gap-2'}>
                        {data.images.map((image, index) => {
                            return <View
                                className={'relative aspect-square bg-gray-100 flex items-center justify-center rounded-[8px] overflow-hidden'}>
                                <Image mode={'aspectFill'} src={getImageUrl(image)} className={'size-[100%]'}/>
                                <View
                                    className={'bg-[#00000099] flex items-center justify-center size-[22px] rounded-bl-[8px] absolute right-0 top-0'}
                                    onClick={() => {
                                        setData(draft => {
                                            draft.images.splice(index, 1);
                                        });
                                    }}
                                >
                                    <Image src={require('../../assets/close.svg')} className={'size-[8px]'}/>
                                </View>
                            </View>;
                        })}
                        {data.images.length < 9 && <View
                            className={'aspect-square flex flex-col items-center justify-center bg-[#F9F9F9] rounded-[8px] gap-3'}
                            onClick={async () => {
                                try {
                                    const {tempFilePaths} = await Taro.chooseImage({count: 9 - data.images.length});
                                    Taro.showLoading();
                                    for (const file of tempFilePaths) {
                                        try {
                                            const {value} = await request({
                                                url: 'upload',
                                                method: 'POST',
                                                file,
                                                data: {
                                                    dir: 'eatery'
                                                }
                                            });
                                            setData(draft => {
                                                draft.images.push(value);
                                            });
                                        } catch {
                                        }
                                    }
                                    Taro.hideLoading();
                                } catch {

                                }
                            }}
                        >
                            <Image src={require('../../assets/plus.svg')} className={'size-[20px]'}/>
                            <View className={'text-[#999999] text-[12px]'}>添加图片</View>
                        </View>}
                    </View>
                </View>
                <View className={'px-[12px] py-[16px] rounded-xl flex flex-col gap-[16px] bg-white'}>
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold'}>店铺类型</View>
                        <RadioGroup direction='horizontal' value={data.type} onChange={value => setData(draft => {
                            draft.type = value;
                        })} className={'gap-[8px]'}>
                            <Radio shape='button' value={1}>美食</Radio>
                            <Radio shape='button' value={2}>特产</Radio>
                        </RadioGroup>
                    </View>
                    <View className={'h-[1px] bg-[#F1F1F1]'}/>
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold'}>店铺名称</View>
                        <Input align={'right'} value={data.name} onChange={value => setData(draft => {
                            draft.name = value;
                        })} placeholder={'请输入店铺名称'}/>
                    </View>
                    <View className={'h-[1px] bg-[#F1F1F1]'}/>
                    <View className={'flex items-center text-[15px]'}>
                        <View className={'font-semibold me-auto'}>营业时间</View>
                        <TimePicker
                            title={'开始时间'}
                            placeholder='请选择'
                            type='hour-minutes'
                            value={data.hours.start}
                            onChange={(value) => {
                                setData(draft => {
                                    draft.hours.start = value;
                                });
                            }}
                        />
                        <View className={'px-2'}>-</View>
                        <TimePicker
                            title={'结束时间'}
                            placeholder='请选择'
                            type='hour-minutes'
                            value={data.hours.end}
                            onChange={(value) => {
                                setData(draft => {
                                    draft.hours.end = value;
                                });
                            }}
                        />
                    </View>
                    {data.type == 1 && <>
                        <View className={'h-[1px] bg-[#F1F1F1]'}/>
                        <View className={'flex items-center justify-between text-[15px] gap-2'}>
                            <View className={'font-semibold'}>人均消费</View>
                            <Input type={'number'} value={data.cost} onChange={value => setData(draft => {
                                draft.cost = value;
                            })} align={'right'} placeholder={'请输入'}/>
                            <Text>元</Text>
                        </View>
                        <View className={'h-[1px] bg-[#F1F1F1]'}/>
                        <View className={'flex items-center justify-between text-[15px]'}>
                            <View className={'font-semibold'}>包间预定</View>
                            <RadioGroup direction='horizontal' value={data.can_reserve}
                                        onChange={value => setData(draft => {
                                            draft.can_reserve = Number(value);
                                        })} className={'gap-[8px]'}>
                                <Radio shape='button' value={1}>支持</Radio>
                                <Radio shape='button' value={0}>不支持</Radio>
                            </RadioGroup>
                        </View>
                    </>}
                </View>
                <View className={'px-[12px] py-[16px] rounded-xl flex flex-col gap-[16px] bg-white'}>
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold me-auto'}>店铺位置</View>
                        <View onClick={async () => {
                            const result = await Taro.chooseLocation({
                                ...data.location,
                                title: '店铺位置',
                            });
                            if (result.name) {
                                try {
                                    const res = await Taro.request({
                                        url: 'https://apis.map.qq.com/ws/geocoder/v1/',
                                        data: {
                                            key: LOCATION_APIKEY,
                                            location: `${result.latitude},${result.longitude}`,
                                        }
                                    });

                                    setData(draft => {
                                        draft.location = {
                                            name: result.name,
                                            district: res.data.result.address_component.district,
                                            latitude: result.latitude,
                                            longitude: result.longitude,
                                        };
                                    });
                                } catch {
                                }
                            }
                        }} className={'text-[#757575]'}>{data.location?.name || '请选择'}</View>
                        <Image src={require('../../assets/right.svg')} className={'size-[16px] ms-1'}/>
                    </View>
                    <View className={'h-[1px] bg-[#F1F1F1]'}/>
                    <View className={'flex flex-col gap-[16px] text-[15px]'}>
                        <View className={'font-semibold'}>详细地址</View>
                        <TextArea className={'h-[50px]'} value={data.address} onChange={value => setData(draft => {
                            draft.address = value;
                        })} placeholder={'请输入详细地址'}/>
                    </View>
                    <View className={'h-[1px] bg-[#F1F1F1]'}/>
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold'}>联系方式</View>
                        <Input type={'number'} value={data.phone} onChange={value => setData(draft => {
                            draft.phone = value;
                        })} align={'right'} placeholder={'请输入手机号'}/>
                    </View>
                </View>
                <View className={'px-[12px] py-[16px] rounded-xl flex flex-col gap-[16px] bg-white'}>
                    <View className={'text-[15px] flex flex-row justify-between items-center'}>
                        <Text className={'font-semibold'}>标签管理</Text>
                    </View>
                    <View className={'flex gap-3.5 flex-wrap'}>
                        {data.tags.map((tag, index) => {
                            return <View
                                className={'px-4 py-2 bg-[#F6F6F6] text-[15px] text-[#666666] relative rounded-[8px]'}>
                                {tag}
                                <View onClick={() => {
                                    setData(draft => {
                                        draft.tags.splice(index, 1);
                                    });
                                }}
                                      className={'size-[20px] rounded-[36px] bg-[#00000099] text-white flex justify-center items-center absolute top-[-8px] right-[-8px]'}>
                                    <Image src={require('../../assets/close.svg')} className={'size-[8px]'}/>
                                </View>
                            </View>;
                        })}
                        <View onClick={async () => {
                            // @ts-ignore
                            const {content} = await Taro.showModal({
                                title: '添加标签',
                                // @ts-ignore
                                placeholderText: '请输入标签',
                                editable: true,
                            });
                            if (content) {
                                setData(draft => {
                                    draft.tags.push(content);
                                });
                            }

                        }}
                              className={'px-4 py-2 border border-[#F6F6F6] text-[15px] text-[#666666] rounded-[8px] flex items-center gap-[4px]'}>
                            <Image src={require('../../assets/plus.svg')} className={'size-[12px]'}/>
                            <Text>添加标签</Text>
                        </View>
                    </View>
                </View>
            </View>
            <View className={'fixed bottom-0 left-0 right-0 bg-white'}>
                <View className={'px-3 py-2 flex'}>
                    <View onClick={async () => {
                        // 根据是否编辑模式决定请求方法
                        await request({
                            url: 'eatery',
                            method: isEdit ? 'PUT' : 'POST',
                            data: {
                                type: data.type,
                                images: data.images,
                                name: data.name,
                                tags: data.tags,
                                hours: data.hours,
                                address: data.address,
                                phone: data.phone,
                                location: data.location,
                                cost: data.cost,
                                can_reserve: data.can_reserve
                            },
                            toast: true
                        });

                        // 如果是从我的页面跳转过来的，直接返回
                        if (params.from === 'user') {
                            Taro.navigateBack();
                        } else {
                            // 否则跳转到我的页面
                            Taro.redirectTo({
                                url: '/pages/user/index'
                            });
                        }
                    }}
                          className={'p-[10px] grow text-center rounded-3xl bg-[#58B4BF] text-white text-[17px] font-semibold'}>
                        保存
                    </View>
                </View>
                <SafeArea position={'bottom'}/>
            </View>
        </View>
        <SafeArea position={'bottom'}/>
    </View>;
}
