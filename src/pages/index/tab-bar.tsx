import { Image, Navigator, Text, View } from '@tarojs/components';

export default function TabBar() {
    return <View className={'bg-white rounded-[8px] flex justify-between px-[24px] py-[8px] z-[2] relative'}>
        <Navigator url={'/pages/eatery/index?type=1'} className={'flex flex-col items-center'}>
            <Image className={'size-[44px]'} src={require('../../assets/meishi.png')} />
            <Text className={'text-[13px] leading-[18px] font-semibold'}>吃</Text>
        </Navigator>
        <Navigator url={'/pages/eatery/index?type=2'} className={'flex flex-col items-center'}>
            <Image className={'size-[44px]'} src={require('../../assets/techan.png')} />
            <Text className={'text-[13px] leading-[18px] font-semibold'}>购</Text>
        </Navigator>
        <Navigator url={'/pages/spot/index'} className={'flex flex-col items-center'}>
            <Image className={'size-[44px]'} src={require('../../assets/wanle.png')} />
            <Text className={'text-[13px] leading-[18px] font-semibold'}>游</Text>
        </Navigator>
        <Navigator url={'/pages/hotel/index'} className={'flex flex-col items-center'}>
            <Image className={'size-[44px]'} src={require('../../assets/zhusu.png')} />
            <Text className={'text-[13px] leading-[18px] font-semibold'}>住</Text>
        </Navigator>
        <Navigator url={'/pages/user/index'} className={'flex flex-col items-center'}>
            <Image className={'size-[44px]'} src={require('../../assets/my.png')} />
            <Text className={'text-[13px] leading-[18px] font-semibold'}>我的</Text>
        </Navigator>
    </View>;
}
