import { Empty, SafeArea, Tabs } from '@nutui/nutui-react-taro';
import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import { getAvatar } from '../../utils';
import request from '../../utils/request';

export default function Reserve() {
    const [tab, setTab] = useState<string | number>(0);

    // 获取预定列表数据
    const { result: reserves = [], refresh, execute } = useRequest({
        url: `eatery/reservation`
    }, {
        manual: true
    });

    useEffect(() => {
        execute({
            data: {
                status: tab
            }
        });
    }, [tab]);

    // 打电话
    const callUser = (mobile: string) => {
        Taro.makePhoneCall({
            phoneNumber: mobile
        });
    };

    // 确认预定
    const confirmReserve = async (id: number) => {
        const { confirm } = await Taro.showModal({
            title: '确认预定',
            content: '确定要接受此预定请求吗？'
        });

        if (confirm) {
            await request({
                url: `eatery/reservation/${id}/confirm`,
                method: 'POST',
                toast: true
            });
            await refresh();
        }
    };

    // 取消预定
    const cancelReserve = async (id: number) => {
        const { confirm } = await Taro.showModal({
            title: '取消预定',
            content: '确定要取消此预定吗？'
        });

        if (confirm) {
            await request({
                url: `eatery/reservation/${id}/cancel`,
                method: 'POST',
                toast: true
            });
            await refresh();
        }
    };

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'预定管理'} />
            </NavBar>
        </Background>
        <Tabs value={tab} onChange={setTab}>
            <Tabs.TabPane value={0} title='待确认' />
            <Tabs.TabPane value={1} title='已确认' />
            <Tabs.TabPane value={2} title='已取消' />
        </Tabs>
        <View className={'p-[12px] pb-[70px]'}>
            <View className={'flex flex-col gap-[12px]'}>
                {reserves.length == 0 && <Empty title='暂无预定' />}
                {reserves.map(reserve => {
                    return <View key={reserve.id} className={'px-[12px] py-[16px] rounded-[12px] bg-white flex items-center justify-between'}>
                        <View className={'flex gap-[8px] items-center'}>
                            <Image className={'size-[44px] rounded-[50%]'} src={getAvatar(reserve.user.avatar)} />
                            <View className={'flex flex-col gap-[4px]'}>
                                <View className={'text-[15px] flex items-center gap-2'}>
                                    {reserve.user.nickname}
                                </View>
                                <View className={'text-[13px] text-[#999999]'}>{reserve.user.mobile}</View>
                                {reserve.reservation_time && <View className={'text-[12px] text-[red]'}>
                                    {reserve.reservation_time} {reserve.people_count || 1}人
                                </View>}
                            </View>
                        </View>
                        <View className={'flex gap-[4px]'}>
                            <View
                                onClick={() => callUser(reserve.user.mobile)}
                                className={'rounded-[50%] bg-[#F6F6F6] size-[32px] flex items-center justify-center'}
                            >
                                <Image className={'size-[18px]'} src={require('../../assets/phone.svg')} />
                            </View>
                            {tab === 0 && (
                                <>
                                    <View
                                        onClick={() => confirmReserve(reserve.id)}
                                        className={'rounded-[50%] bg-[#F6F6F6] size-[32px] flex items-center justify-center'}
                                    >
                                        <Image className={'size-[18px]'} src={require('../../assets/check.svg')} />
                                    </View>
                                    <View
                                        onClick={() => cancelReserve(reserve.id)}
                                        className={'rounded-[50%] bg-[#F6F6F6] size-[32px] flex items-center justify-center'}
                                    >
                                        <Image className={'size-[18px]'} src={require('../../assets/cancel.svg')} />
                                    </View>
                                </>
                            )}
                        </View>
                    </View>;
                })}
            </View>
        </View>
        <SafeArea position={'bottom'} />
    </View>;
}
