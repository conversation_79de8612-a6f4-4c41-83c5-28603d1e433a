import { Empty, SafeArea } from '@nutui/nutui-react-taro';
import { View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import { getAvatar } from '../../utils';
import request from '../../utils/request';

export default function EateryMember() {

    const { result: members = [], refresh } = useRequest({
        url: 'eatery/member'
    });

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'员工管理'} />
            </NavBar>
        </Background>
        <View className={'p-[12px] pb-[70px]'}>
            <View className={'flex flex-col gap-[12px]'}>
                {members.length == 0 && <Empty title='暂无员工' />}
                {members.map(member => {
                    return <View key={member.id} className={'px-[12px] py-[16px] rounded-[12px] bg-white flex items-center justify-between'}>
                        <View className={'flex gap-[8px] items-center'}>
                            <Image className={'size-[44px] rounded-[50%]'} src={getAvatar(member.avatar)} />
                            <View className={'flex flex-col gap-[4px]'}>
                                <View className={'text-[15px]'}>{member.nickname}</View>
                                <View className={'text-[13px] text-[#999999]'}>{member.mobile}</View>
                            </View>
                        </View>
                        <View onClick={async () => {
                            const { confirm } = await Taro.showModal({
                                title: '确定要删除吗？',
                            });
                            if (confirm) {
                                await request({
                                    url: `eatery/member/${member.id}`,
                                    method: 'DELETE',
                                    toast: true
                                });
                                await refresh();
                            }
                        }} className={'rounded-[50%] bg-[#F6F6F6] size-[36px] flex items-center justify-center'}>
                            <Image className={'size-[20px]'} src={require('../../assets/delete.svg')} />
                        </View>
                    </View>;
                })}
            </View>
            <View className={'fixed bottom-0 left-0 right-0 bg-white'}>
                <View className={'px-3 py-2 flex'} onClick={async () => {
                    // @ts-ignore
                    const { content } = await Taro.showModal({
                        title: '添加员工',
                        // @ts-ignore
                        placeholderText: '输入手机号',
                        editable: true,
                    });
                    if (content) {
                        await request({
                            url: 'eatery/member',
                            method: 'POST',
                            data: {
                                mobile: content
                            },
                            toast: true
                        });
                        await refresh();
                        Taro.showToast({
                            title: '添加成功',
                            icon: 'success',
                        });
                    }
                }}>
                    <View className={'p-[10px] grow text-center rounded-3xl bg-[#58B4BF] text-white text-[17px] font-semibold'}>添加员工</View>
                </View>
                <SafeArea position={'bottom'} />
            </View>
        </View>
        <SafeArea position={'bottom'} />
    </View>;
}
