import { Empty, Tabs } from '@nutui/nutui-react-taro';
import { ListView, ScrollView, View } from '@tarojs/components';
import Background from '../../components/background';
import EateryItem from '../../components/eatery-item';
import SpotItem from '../../components/spot-item';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useInfiniteLoading from '../../hooks/use-infinite-loading';
import { useState } from 'react';

export default function() {
    const [tab, setTab] = useState<string | number>('eatery');

    const { data: eateryData } = useInfiniteLoading({
        url: 'user/star/eatery',
    });

    const { data: spotData } = useInfiniteLoading({
        url: 'user/star/spot',
    });

    // 根据当前选中的标签页选择对应的数据
    const currentData = tab === 'eatery' ? eateryData : spotData;

    return <View className={'bg-white min-h-screen flex flex-col'}>
        <Background white>
            <NavBar>
                <PageTitle title={'我的收藏'} />
            </NavBar>
        </Background>
        <Tabs value={tab} onChange={setTab}>
            <Tabs.TabPane value={'eatery'} title='商铺' />
            <Tabs.TabPane value={'spot'} title='景点' />
        </Tabs>
        <View className={'flex-1'}>
            {currentData.length === 0 && <Empty description={'暂无收藏'} />}
            <ScrollView>
                <ListView padding={[15, 15, 15, 15]}>
                    {tab === 'eatery' ?
                        eateryData.map(item => <EateryItem key={item.id} eatery={item} />) :
                        spotData.map(item => <SpotItem key={item.id} spot={item} />)
                    }
                </ListView>
            </ScrollView>
        </View>
    </View>;
}
