import { Empty, <PERSON><PERSON><PERSON>, Swiper } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';
import Taro, { useRouter, useShareAppMessage, useShareTimeline } from '@tarojs/taro';
import { useState } from 'react';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import { isOpen } from '../../utils';
import getImageUrl from '../../utils/get-image-url';
import request from '../../utils/request';

export default function() {
    const { params: { id = 0 } } = useRouter();

    const { result: spot, error, merge } = useRequest<Spot>({
        url: `spot/${id}`,
    });

    // 配置页面转发
    useShareAppMessage(() => {
        if (!spot) return {
            title: '景点详情',
            path: `/pages/spot/detail?id=${id}`,
        };

        return {
            title: `${spot.name} - 景点详情`,
            path: `/pages/spot/detail?id=${id}`,
            imageUrl: spot.images.length > 0 ? getImageUrl(spot.images[0]) : undefined
        };
    });

    // 配置分享到朋友圈
    useShareTimeline(() => {
        if (!spot) return {
            title: '景点详情',
        };

        return {
            title: `${spot.name} - 景点详情`,
            imageUrl: spot.images.length > 0 ? getImageUrl(spot.images[0]) : undefined
        };
    });

    const [current, setCurrent] = useState(0);

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'详情'} />
            </NavBar>
        </Background>
        <View className={'pb-[48px]'}>
            {error && <View className={'p-[12px]'}><Empty status='error' description='加载失败' /></View>}
            {spot && <View className={'flex flex-col gap-[8px]'}>
                <View className={'flex flex-col p-[16px] gap-[16px] bg-white'}>
                    <Swiper
                        defaultValue={current}
                        onChange={(e) => {
                            setCurrent(e.detail.current);
                        }}
                        height={172}
                        indicator={
                            <View className={'rounded-[6px] py-[2px] px-[8px] bg-[#00000080] absolute right-[6px] bottom-[6px] text-white text-[13px]'}>{current + 1}/{spot.images.length}</View>}
                    >
                        {spot.images.map((item) => {
                            return <Swiper.Item key={item}>
                                <Image className={'rounded-[12px] size-full'} mode={'aspectFill'} src={getImageUrl(item)} />
                            </Swiper.Item>;
                        })}
                    </Swiper>
                    <View className={'text-[20px] font-semibold'}>{spot.name}</View>
                    <View className={'flex gap-[8px] items-stretch'}>
                        <View onClick={() => {
                            if (spot.phone) {
                                Taro.makePhoneCall({
                                    phoneNumber: spot.phone
                                });
                            }
                        }} className={'flex flex-col justify-center items-start w-[110px] gap-[4px] p-[8px_12px] rounded-[8px] bg-[rgba(2,181,41,0.10)]'}>
                            {isOpen(spot.hours) ? (
                                <View className={'rounded-[4px] py-[2px] px-[4px] text-white text-[11px] bg-[#02B529]'}>营业中</View>
                            ) : (
                                <View className={'rounded-[4px] py-[2px] px-[4px] text-white text-[11px] bg-[#999999]'}>休息中</View>
                            )}
                            <View className={'text-[13px]'}>{spot.hours.start}-{spot.hours.end}</View>
                            <View className={'flex items-center gap-[4px] text-[12px] text-[#999]'}>
                                <Image className={'size-[12px]'} src={require('../../assets/phone.svg')} />
                                电话联系
                            </View>
                        </View>
                        <View onClick={() => {
                            if (spot.location) {
                                Taro.openLocation({
                                    latitude: spot.location.latitude,
                                    longitude: spot.location.longitude,
                                    name: spot.name,
                                    address: spot.address
                                });
                            }
                        }} className={'relative flex-1 flex items-center gap-[8px] justify-between self-stretch rounded-[8px] overflow-hidden p-[8px_12px]'}>
                            <Image mode={'aspectFill'} className={'absolute w-full h-full left-0 top-0'} src={require('../../assets/map.png')} />
                            <View className={'text-[14px] font-semibold relative z-[1]'}>{spot.address}</View>
                            <View className={'relative z-[1] flex flex-col gap-[4px] items-center'}>
                                <Image className={'size-[24px]'} src={require('../../assets/navigation-red.svg')} />
                                <View className={'text-[11px]'}>地图</View>
                            </View>
                        </View>
                    </View>
                </View>
                <View className={'bg-white p-[16px]'}>
                    <View className={'flex items-center gap-[8px]'}>
                        <View className={'size-[20px] flex items-center justify-center rounded-[4px] bg-[#58B4BF]'}>
                            <Image className={'size-[16px]'} src={require('../../assets/spot.svg')} />
                        </View>
                        <View className={'text-[15px] font-semibold'}>景区项目</View>
                    </View>
                    {spot.tickets.length > 0 && <View className={'flex flex-col mt-[12px]'}>
                        {spot.tickets.map((item) => {
                            return <>
                                <View />
                                <View className={'py-[12px] border-b-[1px] border-b-[rgba(0,0,0,0.05)] last:border-0 last:pb-0'}>
                                    <View className={'text-[16px] font-semibold'}>{item.name}</View>
                                    <View className={'text-[15px] text-[#F4495C]'}>￥<Text className={'text-[24px]'}>{item.price}</Text></View>
                                </View>
                            </>;
                        })}
                    </View>}
                </View>
                <View className={'fixed bottom-0 left-0 right-0 bg-white shadow-[0_-1px_1px_0_rgba(0,0,0,0.05)]'}>
                    <View className={'flex items-center h-[48px]'}>
                        <View onClick={async () => {
                            await request({
                                url: `spot/${id}/like`,
                                method: 'POST',
                                checkLogin: true
                            });
                            merge({ result: { ...spot, liked: !spot.liked } });
                        }} className={`flex-1 flex items-center justify-center text-[15px] gap-[4px] ${spot.liked ? 'text-[#F4495C]' : ''}`}>
                            <Image className={'size-[24px]'} src={require(spot.liked ? '../../assets/likes-red.svg' : '../../assets/likes.svg')} />
                            点赞
                        </View>
                        <View className={'h-[17px] w-[1px] bg-[#F1F1F1]'} />
                        <View onClick={async () => {
                            await request({
                                url: `spot/${id}/star`,
                                method: 'POST',
                                checkLogin: true
                            });
                            merge({ result: { ...spot, starred: !spot.starred } });
                        }} className={`flex-1 flex items-center justify-center text-[15px] gap-[4px] ${spot.starred ? 'text-[#F4495C]' : ''}`}>
                            <Image className={'size-[24px]'} src={require(spot.starred ? '../../assets/star-red.svg' : '../../assets/star.svg')} />
                            收藏
                        </View>
                    </View>
                    <SafeArea position={'bottom'} />
                </View>
            </View>}
        </View>
        <SafeArea position={'bottom'} />
    </View>;
}
