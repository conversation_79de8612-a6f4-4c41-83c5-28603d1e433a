import { SafeArea } from '@nutui/nutui-react-taro';
import { Image, Navigator, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import useUser from '../../hooks/use-user';
import { getAvatar } from '../../utils';
import EateryManagementCard from './components/eatery-management-card';
import HotelManagementCard from './components/hotel-management-card';
import SpotManagementCard from './components/spot-management-card';

const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
        return '早上好';
    } else if (hour < 18) {
        return '下午好';
    } else {
        return '晚上好';
    }
};

export default function Index() {
    const user = useUser();

    if (!user) {
        return null;
    }

    const { result: coupon } = useRequest({
        url: 'user/coupon/valid'
    });

    return <View className={'bg-white min-h-screen flex flex-col'}>
        <Background>
            <NavBar>
                <PageTitle />
            </NavBar>
            <View className={'p-3 flex gap-3 items-center'}>
                <Image className={'size-[64px] rounded-[50%]'} src={getAvatar(user.avatar)} />
                <View className={'flex flex-col gap-1'}>
                    <View className={'text-[20px] font-semibold flex items-center gap-2'}>
                        <View>{user.nickname}</View>
                    </View>
                    <View className={'text-[13px] text-[#666666]'}>{getGreeting()}，欢迎来到吴忠</View>
                </View>
            </View>
        </Background>
        <View className={'flex flex-col flex-1 justify-between'}>
            <View className={'p-[15px] flex flex-col gap-3'}>
                {coupon && <View className={'p-[1px] pt-[3px] bg-[#ACDADF]  rounded-[12px]'}>
                    <View className={'bg-white rounded-[12px] p-[16px_12px] flex items-center'}>
                        <View className={'flex-1 flex flex-col gap-[5px]'}>
                            <View className={'text-[17px] font-semibold'}>您有一张消费券待使用</View>
                            <View className={'text-[13px] text-[#999]'}>向商家出示二维码可抵扣{coupon.amount}元</View>
                        </View>
                        <Navigator url={`/pages/coupon/detail?id=${coupon.pivot.id}`} className={'p-[8px_12px] rounded-[20px] text-white text-[13px] bg-[#58B4BF] font-semibold'}>出示二维码</Navigator>
                    </View>
                </View>}
                <EateryManagementCard />
                <SpotManagementCard />
                <HotelManagementCard />
                <Navigator url={'/pages/user/coupon'} className={'shadow-custom rounded-[12px] p-[15px] flex items-center gap-2 text-[15px]'}>
                    <Image className={'size-5'} src={require('../../assets/ticket.svg')} />
                    消费券
                    <Image className={'size-5 ms-auto'} src={require('../../assets/right.svg')} />
                </Navigator>
                <Navigator url={'/pages/user/star'} className={'shadow-custom rounded-[12px] p-[15px] flex items-center gap-2 text-[15px]'}>
                    <Image className={'size-5'} src={require('../../assets/favorite.svg')} />
                    我的收藏
                    <Image className={'size-5 ms-auto'} src={require('../../assets/right.svg')} />
                </Navigator>
            </View>
            <View className={'px-[15px]'}>
                <View onClick={() => {
                    Taro.removeStorageSync('user');
                    Taro.redirectTo({
                        url: '/pages/index/index'
                    });
                }} className={'mb-[15px] rounded-[10px] bg-[#F6F6F6] text-[#F4495C] text-[16px] p-[13px] flex items-center justify-center'}>退出登录</View>
                <SafeArea position={'bottom'} />
            </View>
        </View>
    </View>;
}
