import { View } from '@tarojs/components';
import { getMenuButtonBoundingClientRect, getWindowInfo } from '@tarojs/taro';
import { ReactNode } from 'react';

interface Props {
    children?: ReactNode;
}

export default function NavBar({ children }: Props) {
    const { statusBarHeight = 0, } = getWindowInfo() || {};
    const menu = getMenuButtonBoundingClientRect();
    const height = menu ? (menu.height + (menu.top - statusBarHeight) * 2) : 40;

    return <View>
        <View className={'block w-full'} style={{ paddingTop: statusBarHeight }} />
        <View className={'flex items-center'} style={{ height }}>
            {children}
        </View>
    </View>;
}
