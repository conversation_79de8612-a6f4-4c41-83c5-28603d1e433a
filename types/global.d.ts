/// <reference types="@tarojs/taro" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {

    interface ProcessEnv {
        /** NODE 内置环境变量, 会影响到最终构建生成产物 */
        NODE_ENV: 'development' | 'production',
        /** 当前构建的平台 */
        TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd'
        /**
         * 当前构建的小程序 appid
         * @description 若不同环境有不同的小程序，可通过在 env 文件中配置环境变量`TARO_APP_ID`来方便快速切换 appid， 而不必手动去修改 dist/project.config.json 文件
         * @see https://taro-docs.jd.com/docs/next/env-mode-config#特殊环境变量-taro_app_id
         */
        TARO_APP_ID: string
    }
}

declare const LOCATION_APIKEY: string;
declare const HOST: string;

interface PaginationType<T = any> {
    total: number;
    current_page: number;
    per_page: number;
    last_page: number;
    data: T[];
}

interface Eatery {
    id: string;
    type: number | string;
    images: string[];
    name: string;
    tags: string[];
    hours: { start: string, end: string };
    cost: string;
    address: string;
    phone?: string;
    location?: {
        name: string,
        latitude: number,
        longitude: number,
        district: string
    };
    can_reserve?: number;
    views?: number;
    likes?: number;
    starred?: boolean;
    liked?: boolean;
    reserved?: number; //-1 表示不支持预定 0 表示未预定 1 表示确认中 2 表示已确认;
    status?: number; // 审核状态: 0-审核中, 1-已通过, 2-未通过
    pivot?: {
        access_level: number; // 权限级别: 60-管理员
    };
}

interface Spot {
    id: string;
    images: string[];
    name: string;
    hours: { start: string, end: string };
    address: string;
    phone: string;
    location?: {
        name: string,
        latitude: number,
        longitude: number,
    };
    tickets?: { name: string, price: number }[];
    views?: number;
    likes?: number;
    starred?: boolean;
    liked?: boolean;
    status?: number; // 审核状态: 0-审核中, 1-已通过, 2-未通过
    pivot?: {
        access_level: number; // 权限级别: 60-管理员
    };
}

interface Message {
    query: string;
    content: string;
    loading?: boolean;
}

interface Conversation {
    id?: string;
    messages: Message[];
}

interface Hotel {
    id: string;
    images: string[];
    name: string;
    type: string;
    price: string;
    path: string;
    address: string;
    location?: {
        name: string,
        latitude: number,
        longitude: number,
    };
    rating: {
        score: number,
        comment: string,
    };
    status?: number; // 审核状态: 0-审核中, 1-已通过, 2-未通过
    pivot?: {
        access_level: number; // 权限级别: 60-管理员
    };
}
