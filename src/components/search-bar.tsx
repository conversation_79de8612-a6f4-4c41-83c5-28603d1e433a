import { Input, View, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import SearchIcon from '../assets/search.svg';

interface Props {
    placeholder?: string;
    type?: 'eatery' | 'spot' | 'hotel';
}

export default function SearchBar({ placeholder, type = 'eatery' }: Props) {
    const handleNavigateToSearch = () => {
        Taro.navigateTo({
            url: `/pages/search/index?type=${type}`
        });
    };

    return <View
        className={'border-2 border-[rgba(18,18,18,1)] flex justify-between items-center rounded-[20px] p-[4px_4px_4px_16px] my-[5px] mb-[6px] bg-white'}
        onClick={handleNavigateToSearch}
    >
        <Input className={'flex-1'} type='text' placeholder={placeholder} disabled />
        <View className={'flex items-center justify-center rounded-[22px] p-[6px_16px] bg-[rgba(18,18,18,1)]'}>
            <Image className={'w-[16px] h-[16px]'} src={SearchIcon} />
        </View>
    </View>;
}
