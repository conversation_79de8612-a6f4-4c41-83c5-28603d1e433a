import { Empty, Input, Popup, Safe<PERSON>rea, Swiper } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';
import Taro, { useRouter, useShareAppMessage, useShareTimeline } from '@tarojs/taro';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useImmer } from 'use-immer';
import Background from '../../components/background';
import DatePicker from '../../components/date-picker';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import TimeRangePicker from '../../components/time-range-picker';
import useRequest from '../../hooks/use-request';
import { isOpen } from '../../utils';
import getImageUrl from '../../utils/get-image-url';
import request from '../../utils/request';

// 获取预定状态文本
function getReservationStatusText(status?: number) {
    switch (status) {
        case 1:
            return '确认中';
        case 2:
            return '已预定';
        case 3:
            return '已取消';
        case 0:
            return '预定包间';
        default:
            return '预定包间';
    }
}

export default function Detail() {

    const { params: { id = 0 } } = useRouter();

    const { result: eatery, error, merge } = useRequest<Eatery>({
        url: `eatery/${id}`,
    });

    // 预定表单状态
    const [showReserveForm, setShowReserveForm] = useState(false);

    // 计算默认时间：当前时间的两个小时后的整点
    const getDefaultTime = () => {
        const now = new Date();
        const currentHour = now.getHours();
        const targetHour = currentHour + 2;
        const adjustedHour = targetHour >= 24 ? targetHour - 24 : targetHour;
        const endHour = adjustedHour + 2 >= 24 ? adjustedHour + 2 - 24 : adjustedHour + 2;

        // 格式化小时，确保是两位数
        const formatHour = (hour) => hour.toString().padStart(2, '0');

        return {
            date: dayjs().format('YYYY-MM-DD'),
            startTime: formatHour(adjustedHour),
            endTime: formatHour(endHour)
        };
    };

    const defaultTime = getDefaultTime();
    const [reserveData, setReserveData] = useImmer({
        date: defaultTime.date,
        startTime: defaultTime.startTime,
        endTime: defaultTime.endTime,
        people: '2'
    });

    // 配置页面转发
    useShareAppMessage(() => {
        if (!eatery) return {
            title: '美食详情',
            path: `/pages/eatery/detail?id=${id}`,
        };

        return {
            title: `${eatery.name} - 美食详情`,
            path: `/pages/eatery/detail?id=${id}`,
            imageUrl: eatery.images.length > 0 ? getImageUrl(eatery.images[0]) : undefined
        };
    });

    // 配置分享到朋友圈
    useShareTimeline(() => {
        if (!eatery) return {
            title: '美食详情',
        };

        return {
            title: `${eatery.name} - 美食详情`,
            imageUrl: eatery.images.length > 0 ? getImageUrl(eatery.images[0]) : undefined
        };
    });

    const [current, setCurrent] = useState(0);

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'详情'} />
            </NavBar>
        </Background>
        <View className={'pb-[48px]'}>
            {error && <View className={'p-[12px]'}><Empty status='error' description='加载失败' /></View>}
            {eatery && <View className={'flex flex-col gap-[8px]'}>
                <View className={'flex flex-col p-[16px] gap-[16px] bg-white'}>
                    <Swiper
                        defaultValue={current}
                        onChange={(e) => {
                            setCurrent(e.detail.current);
                        }}
                        height={172}
                        indicator={
                            <View className={'rounded-[6px] py-[2px] px-[8px] bg-[#00000080] absolute right-[6px] bottom-[6px] text-white text-[13px]'}>{current + 1}/{eatery.images.length}</View>}
                    >
                        {eatery.images.map((item) => {
                            return <Swiper.Item key={item}>
                                <Image className={'rounded-[12px] size-full'} mode={'aspectFill'} src={getImageUrl(item)} />
                            </Swiper.Item>;
                        })}
                    </Swiper>
                    <View>
                        <View className={'text-[20px] font-semibold'}>{eatery.name}</View>
                        {eatery.type == 1 && eatery.cost &&
                            <View className={'text-[14px] text-[#333333] mt-[8px]'}>￥{eatery.cost}/人</View>}
                    </View>
                    <View className={'h-[1px] bg-[#0000001A]'} />
                    <View className={'flex items-center gap-[16px]'}>
                        <View className={'flex flex-1 items-center gap-[8px]'}>
                            <View className={`rounded-[4px] py-[2px] px-[4px] gap-[10px] text-white text-[11px] ${isOpen(eatery.hours) ? 'bg-[#02B529]' : 'bg-[#999999]'}`}>
                                {isOpen(eatery.hours) ? '营业中' : '休息中'}
                            </View>
                            <View className={'text-[12px] text-[#666666]'}>{eatery.hours.start}-{eatery.hours.end}</View>
                        </View>
                        {!!eatery.can_reserve && <View onClick={async () => {
                            // 只有未预定、确认中或已取消状态才能点击并发送请求
                            if (eatery.reserved === 0 || eatery.reserved === 1 || eatery.reserved === 3 || eatery.reserved === undefined) {
                                if (eatery.reserved === 1) {
                                    // 确认中状态，询问是否取消预定
                                    const { confirm } = await Taro.showModal({
                                        title: '取消预定',
                                        content: '确定要取消预定吗？'
                                    });

                                    // 只有用户确认后才执行操作
                                    if (confirm) {
                                        await request({
                                            url: `eatery/${id}/reserve`,
                                            method: 'POST',
                                            checkLogin: true
                                        });

                                        // 变为已取消状态
                                        merge({ result: { ...eatery, reserved: 0 } });
                                    }
                                } else {
                                    // 未预定或已取消状态，显示预定表单
                                    setShowReserveForm(true);
                                }
                            }
                        }} className={clsx(
                            'flex p-[6px_12px] justify-center items-center gap-[10px] rounded-[20px] text-white text-[13px] font-semibold',
                            {
                                'bg-[#FF724C]': eatery.reserved === 0 || eatery.reserved === undefined, // 未预定状态，红色
                                'bg-[#FFA07A]': eatery.reserved === 1, // 确认中状态，淡橙色
                                'bg-[#4CAF50]': eatery.reserved === 2, // 已预定状态，绿色
                                'bg-[#999999]': eatery.reserved === 3, // 已取消状态，灰色
                            }
                        )}>
                            {getReservationStatusText(eatery.reserved)}
                        </View>}
                    </View>
                    {eatery.tags.length > 0 && <View className={'text-[12px] flex gap-[6px] flex-wrap'}>
                        {eatery.tags.map(tag =>
                            <Text className={'text-nowrap text-[12px] rounded-[4px] py-[2px] px-[8px] text-[#666666] bg-[#F6F6F6]'}>{tag}</Text>)}
                    </View>}
                    <View className={'h-[1px] bg-[#0000001A]'} />
                    <View className={'flex items-center gap-[16px]'}>
                        <View className={'flex flex-1 items-center gap-[8px] text-[16px]'}>
                            <Image className={'size-[16px] flex-shrink-0'} src={require('../../assets/local.svg')} />
                            {eatery.address}
                        </View>
                        <View className={'h-[28px] w-[1px] bg-[#0000001A]'} />
                        {eatery.phone && (
                            <>
                                <View className={'flex flex-col items-center gap-[4px]'} onClick={() => {
                                    Taro.makePhoneCall({
                                        phoneNumber: eatery.phone
                                    });
                                }}>
                                    <Image className={'size-[24px]'} src={require('../../assets/phone-filled.svg')} />
                                    <View className={'text-[11px] text-[#666666]'}>电话联系</View>
                                </View>
                                <View className={'h-[28px] w-[1px] bg-[#0000001A]'} />
                            </>
                        )}
                        <View className={'flex flex-col items-center gap-[4px]'} onClick={() => {
                            Taro.openLocation({
                                latitude: eatery.location.latitude,
                                longitude: eatery.location.longitude,
                                name: eatery.name,
                                address: eatery.address
                            });
                        }}>
                            <Image className={'size-[24px]'} src={require('../../assets/navigation.svg')} />
                            <View className={'text-[11px] text-[#666666]'}>导航到店</View>
                        </View>
                    </View>
                </View>
                {eatery.foods.length > 0 && <View className={'bg-white p-[16px]'}>
                    <View className={'flex items-center gap-[8px]'}>
                        <View className={'size-[20px] flex items-center justify-center rounded-[4px] bg-[#58B4BF]'}>
                            <Image className={'size-[16px]'} src={require('../../assets/bowl-white.svg')} />
                        </View>
                        <View className={'text-[15px] font-semibold'}>本店{eatery.type == 1 ? '菜品' : '特产'}</View>
                    </View>
                    <View className={'grid grid-cols-3 gap-[9px] mt-[12px]'}>
                        {eatery.foods.map((item) => {
                            return <View className={'flex flex-col items-center gap-[8px] font-semibold mb-[4px]'}>
                                <View className={'aspect-square rounded-[8px] w-full flex-1 overflow-hidden'}>
                                    <Image mode={'aspectFill'} className={'size-full'} src={getImageUrl(item.cover)} />
                                </View>
                                <View className={'text-[15px] whitespace-nowrap text-ellipsis w-full overflow-hidden text-center'}>{item.name}</View>
                                <View className={'text-[13px] text-[#FF724C]'}>￥<Text className={'text-[17px]'}>{item.price}</Text></View>
                            </View>;
                        })}
                    </View>
                </View>}
                <View className={'fixed bottom-0 left-0 right-0 bg-white shadow-[0_-1px_1px_0_rgba(0,0,0,0.05)]'}>
                    <View className={'flex items-center h-[48px]'}>
                        <View onClick={async () => {
                            await request({
                                url: `eatery/${id}/like`,
                                method: 'POST',
                                checkLogin: true
                            });
                            merge({ result: { ...eatery, liked: !eatery.liked } });
                        }} className={`flex-1 flex items-center justify-center text-[15px] gap-[4px] ${eatery.liked ? 'text-[#F4495C]' : ''}`}>
                            <Image className={'size-[24px]'} src={require(eatery.liked ? '../../assets/likes-red.svg' : '../../assets/likes.svg')} />
                            点赞
                        </View>
                        <View className={'h-[17px] w-[1px] bg-[#F1F1F1]'} />
                        <View onClick={async () => {
                            await request({
                                url: `eatery/${id}/star`,
                                method: 'POST',
                                checkLogin: true
                            });
                            merge({ result: { ...eatery, starred: !eatery.starred } });
                        }} className={`flex-1 flex items-center justify-center text-[15px] gap-[4px] ${eatery.starred ? 'text-[#F4495C]' : ''}`}>
                            <Image className={'size-[24px]'} src={require(eatery.starred ? '../../assets/star-red.svg' : '../../assets/star.svg')} />
                            收藏
                        </View>
                    </View>
                    <SafeArea position={'bottom'} />
                </View>
            </View>}
        </View>
        <SafeArea position={'bottom'} />

        {/* 预定表单弹窗 */}
        {eatery && <Popup
            visible={showReserveForm}
            position='bottom'
            destroyOnClose
            title={'预定包间'}
            round
            onClose={() => setShowReserveForm(false)}
        >
            <View className={'p-[16px]'}>
                <View className={'flex flex-col gap-[16px]'}>
                    {/* 日期和时间选择 */}
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold'}>预定时间</View>
                        <View className={'flex items-center gap-[8px]'}>
                            <DatePicker
                                title={'选择日期'}
                                placeholder='日期'
                                type='date'
                                value={reserveData.date}
                                onChange={(value) => {
                                    setReserveData(draft => {
                                        draft.date = value;
                                    });
                                }}
                            />
                            <View className={'px-1'}></View>
                            <TimeRangePicker
                                title={'预定时间范围'}
                                placeholder='选择时间范围'
                                startTime={reserveData.startTime}
                                endTime={reserveData.endTime}
                                onChange={(startTime, endTime) => {
                                    setReserveData(draft => {
                                        draft.startTime = startTime;
                                        draft.endTime = endTime;
                                    });
                                }}
                            />
                        </View>
                    </View>

                    {/* 人数选择 */}
                    <View className={'flex items-center justify-between text-[15px]'}>
                        <View className={'font-semibold'}>预定人数</View>
                        <Input
                            type={'digit'}
                            value={reserveData.people}
                            onChange={value => {
                                setReserveData(draft => {
                                    draft.people = value;
                                });
                            }}
                            placeholder={'请输入人数'}
                            align={'right'}
                            style={{ width: '120px' }}
                        />
                        <View>人</View>
                    </View>
                </View>
                <View className={'mt-[24px] flex gap-[12px]'}>
                    <View
                        className={'flex-1 p-[10px] text-center rounded-[20px] bg-[#F6F6F6] text-[#666666]'}
                        onClick={() => setShowReserveForm(false)}
                    >
                        取消
                    </View>
                    <View
                        className={'flex-1 p-[10px] text-center rounded-[20px] bg-[#FF724C] text-white'}
                        onClick={async () => {
                            // 验证表单
                            if (!reserveData.date) {
                                Taro.showToast({
                                    title: '请选择预定日期',
                                    icon: 'none'
                                });
                                return;
                            }

                            if (!reserveData.startTime || !reserveData.endTime) {
                                Taro.showToast({
                                    title: '请选择完整的预定时间',
                                    icon: 'none'
                                });
                                return;
                            }

                            if (!reserveData.people) {
                                Taro.showToast({
                                    title: '请输入预定人数',
                                    icon: 'none'
                                });
                                return;
                            }

                            // 发送预定请求
                            try {
                                await request({
                                    url: `eatery/${id}/reserve`,
                                    method: 'POST',
                                    data: {
                                        reservation_time: `${reserveData.date} ${reserveData.startTime}点 - ${reserveData.endTime}点`,
                                        people_count: reserveData.people
                                    },
                                    checkLogin: true,
                                    toast: true
                                });

                                // 更新预定状态为确认中
                                merge({ result: { ...eatery, reserved: 1 } });

                                // 关闭弹窗
                                setShowReserveForm(false);

                                // 显示成功提示
                                Taro.showToast({
                                    title: '预定成功',
                                    icon: 'success'
                                });
                            } catch (error) {
                                console.error('预定失败', error);
                            }
                        }}
                    >
                        确认预定
                    </View>
                </View>
                <SafeArea position={'bottom'} />
            </View>
        </Popup>}
    </View>;
}
