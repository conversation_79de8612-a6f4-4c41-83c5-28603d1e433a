import { RichText } from '@tarojs/components';
import clsx from 'clsx';
import MarkdownIt from 'markdown-it';
import { useMemo } from 'react';

const md = new MarkdownIt();

interface Props {
    className?: string;
    content: string;
}

export default function Markdown({ className, content }: Props) {

    const html = useMemo(() => {
        return md.render(content);
    }, [content]);

    return <RichText className={clsx('markdown', className)} nodes={html} />;
}
