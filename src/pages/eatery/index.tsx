import { ListView, <PERSON><PERSON>View, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useState } from 'react';
import queryString from 'query-string';
import Background from '../../components/background';
import EateryItem from '../../components/eatery-item';
import FilterSortBar from '../../components/filter-sort-bar';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import SearchBar from '../../components/search-bar';
import useInfiniteLoading from '../../hooks/use-infinite-loading';

export default function() {
    const { params: { type = 1 } } = useRouter();
    const [filterParams, setFilterParams] = useState({});

    // 根据type参数确定标题
    const title = type == 1 ? '美食' : '特产';

    // 处理筛选和排序变化
    const handleFilterChange = (params) => {
        setFilterParams(params);
    };

    // 构建 URL
    const url = queryString.stringifyUrl({
        url: 'eatery',
        query: {
            type,
            ...filterParams
        }
    });

    const { data } = useInfiniteLoading({
        url,
    });

    // 处理页面点击事件，用于关闭下拉菜单
    const handlePageClick = () => {
        Taro.eventCenter.trigger('page:click');
    };

    return <View className={'bg-white min-h-screen'} onClick={handlePageClick}>
        <Background>
            <NavBar>
                <PageTitle title={title} />
            </NavBar>
            <SearchBar placeholder={'搜索商户'} type='eatery' />
            <FilterSortBar onFilterChange={handleFilterChange} />
        </Background>
        <View className={'bg-[#F6F6F6] h-[8px]'} />
        <ScrollView>
            <ListView padding={[15, 15, 15, 15]}>
                {data.map((item) => {
                    return <EateryItem eatery={item} />;
                })}
            </ListView>
        </ScrollView>
    </View>;
}
