import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import getImageUrl from '../utils/get-image-url';

interface Props {
    hotel: Hotel;
}

export default function HotelItem({ hotel }: Props) {
    const navigateToExternalMiniProgram = () => {
        Taro.navigateToMiniProgram({
            appId: 'wx0e6ed4f51db9d078',
            path: hotel.path
        });
    };

    return <View onClick={navigateToExternalMiniProgram} className={'flex gap-[10px] relative mb-[10px] pb-[10px] after:absolute after:left-[106px] after:right-0 after:bottom-0 after:content-[\'\'] after:border-b after:border-[#f1f1f1]'}>
        <View className={'size-[96px] flex-shrink-0 rounded-[8px] relative overflow-hidden'}>
            <Image className={'size-full'} mode={'aspectFill'} src={getImageUrl(hotel.images[0])} />
            <View className={'absolute bottom-0 left-0 bg-black/50 p-[4px_6px] rounded-tr-[6px] rounded-bl-[8px] text-[11px] text-white'}>{hotel.type}</View>
        </View>
        <View className={'flex flex-col flex-1 overflow-hidden'}>
            <View className={'text-[16px] font-semibold mb-[8px]'}>{hotel.name}</View>
            <View className={'flex items-center gap-[8px] text-[#2285EF]'}>
                <View className={'text-[13px] flex-shrink-0'}>
                    <Text className={'text-[20px] font-semibold'}>{hotel.rating.score}</Text>
                    分
                </View>
                <View className={'text-[13px] whitespace-nowrap'}>“{hotel.rating.comment}”</View>
            </View>
            <View className={'flex items-center justify-between gap-[8px] mt-auto text-[12px] text-[#666] overflow-hidden'}>
                <View className={'whitespace-nowrap overflow-hidden text-ellipsis'}>{hotel.address}</View>
                <View className={'flex-shrink-0'}><Text className={'text-[#F4495C]'}>￥<Text className={'text-[20px]'}>{hotel.price}</Text></Text>起</View>
            </View>
        </View>
    </View>;
}
