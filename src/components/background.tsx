import { View } from '@tarojs/components';
import { ReactNode } from 'react';
import clsx from 'clsx';

interface Props {
  children: ReactNode;
  className?: string;
  white?: boolean;
}

export default function Background({ children, className, white = false }: Props) {
  return (
    <View
      className={clsx(
        'sticky top-0 px-[15px] z-10 overflow-hidden',
        white ? 'bg-white' : 'bg-white bg-gradient-to-b from-[rgba(88,180,191,0.3)] to-[rgba(88,180,191,0)]',
        className
      )}
    >
      {children}
    </View>
  );
}
