import { Image, Navigator, Text, View } from '@tarojs/components';
import getImageUrl from '../utils/get-image-url';
import { isOpen } from '../utils';

interface Props {
    eatery: Eatery;
}

export default function EateryItem({ eatery }: Props) {
    return <Navigator url={`/pages/eatery/detail?id=${eatery.id}`} className={'flex gap-[10px] relative mb-[10px] pb-[10px] after:absolute after:left-[106px] after:right-0 after:bottom-0 after:content-[\'\'] after:border-b after:border-[#f1f1f1]'}>
        <View className={'size-[96px] flex-shrink-0 rounded-[8px] relative overflow-hidden'}>
            <Image className={'size-full'} mode={'aspectFill'} src={getImageUrl(eatery.images[0])} />
            <View className={'absolute bottom-0 left-0 bg-[#FF724C] p-[2px_4px] rounded-tr-[6px] rounded-bl-[8px] text-[11px] text-white'}>消费券</View>
        </View>
        <View className={'flex gap-[8px] flex-col flex-1'}>
            <View className={'text-[16px] font-semibold'}>{eatery.name}</View>
            <View className={'flex items-center gap-[8px]'}>
                {isOpen(eatery.hours) ? (
                    <View className={'rounded-[4px] p-[2px_4px] gap-[10px] text-white text-[11px] bg-[#02B529]'}>营业中</View>
                ) : (
                    <View className={'rounded-[4px] p-[2px_4px] gap-[10px] text-white text-[11px] bg-[#999999]'}>休息中</View>
                )}
                <Text className={'text-[12px] text-[#666666]'}>{eatery.hours.start}-{eatery.hours.end}</Text>
            </View>
            <View className={'flex items-center gap-[8px] text-[#666666] text-[13px]'}>
                <View>{eatery.views}人浏览</View>
                <View className={'w-[1px] bg-[#cccccc] h-[10px]'} />
                <View>{eatery.likes}人点赞</View>
            </View>
            {eatery.tags.length > 0 && <View className={'text-[12px] flex gap-[6px] flex-wrap'}>
                {eatery.tags.splice(0, 3).map(tag =>
                    <Text className={'whitespace-nowrap rounded-[4px] p-[2px_8px] text-[#666666] bg-[#F6F6F6]'}>{tag}</Text>)}
            </View>}
        </View>
    </Navigator>;
}
