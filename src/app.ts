import Taro, { useDidShow, useLaunch } from '@tarojs/taro';
import { PropsWithChildren } from 'react';
// 全局样式
import './app.scss';
import request from './utils/request';

export default function App(props: PropsWithChildren) {

    useDidShow(async () => {
        //获取位置信息
        try {
            const result = await Taro.getLocation({
                type: 'wgs84'
            });

            const location = {
                latitude: result.latitude,
                longitude: result.longitude,
                address: undefined
            };

            try {
                const res = await Taro.request({
                    url: 'https://apis.map.qq.com/ws/geocoder/v1/',
                    data: {
                        key: LOCATION_APIKEY,
                        location: `${result.latitude},${result.longitude}`,
                    }
                });

                location.address = res.data.result.address_reference.famous_area.title;
            } catch {

            }

            Taro.setStorageSync('location', location);
            Taro.eventCenter.trigger('location', location);
        } catch {
            Taro.removeStorageSync('location');
        }
    });

    useLaunch(async () => {
        //更新用户信息
        try {
            const user = await request({ url: 'auth/current' });
            Taro.setStorageSync('user', user);
        } catch {
            Taro.removeStorageSync('user');
        }
    });

    return props.children;
}
