import Taro, { usePullDownRefresh, useReachBottom } from '@tarojs/taro';
import queryString from 'query-string';
import { useCallback, useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import { isPaginationObject } from '../utils';
import request from '../utils/request';

type Params = {
    page?: number;
    [key: string]: any;
};

interface Option {
    url: string;
    getState?: (result: any) => { params: Params, end: boolean, data: any[] };
    limit?: number;
}

export default function useInfiniteLoading({ url, getState, limit = 10 }: Option) {
    const [loading, setLoading] = useState(false);
    const [state, setState] = useImmer<{ params: Params, end: boolean, data: any[] }>({
        params: {},
        end: false,
        data: []
    });

    const loadData = useCallback(async (refresh: boolean = false) => {
        if (!loading) {
            setLoading(true);

            try {
                let result: any[];

                const params: Params = refresh ? {} : state.params;

                result = await request({
                    url: queryString.stringifyUrl({
                        url,
                        query: params
                    }),
                });

                if (!getState) {
                    getState = (result: any[]) => {
                        if (isPaginationObject(result)) {
                            return {
                                params: {
                                    page: result.current_page + 1,
                                },
                                end: result.current_page >= result.last_page,
                                data: result.data
                            };
                        } else {
                            return {
                                params: {
                                    offset: result[result.length - 1]?.id,
                                },
                                end: result.length < limit,
                                data: result
                            };
                        }
                    };
                }

                const newState = getState(result);

                setState({
                    ...newState,
                    data: refresh ? newState.data : [...state.data, ...newState.data]
                });
            } catch {
                setState({
                    ...state,
                    end: true
                });
            } finally {
                setLoading(false);
            }
        }
    }, [url, getState, state, loading, limit]);

    const reload = useCallback(() => {
        loadData(true);
    }, [loadData]);

    useEffect(() => {
        loadData(true);
    }, [url]);

    usePullDownRefresh(() => {
        reload();
        Taro.stopPullDownRefresh();
    });

    useReachBottom(() => {
        if (!state.end) {
            loadData();
        }
    });

    return { data: state.data, hasMore: !state.end, loading, reload };
}
