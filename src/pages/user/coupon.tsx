import { Empty, Tabs } from '@nutui/nutui-react-taro';
import { ListView, ScrollView, View, Text, Navigator } from '@tarojs/components';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useInfiniteLoading from '../../hooks/use-infinite-loading';
import { useState } from 'react';
import dayjs from 'dayjs';

export default function() {
    const [tab, setTab] = useState<string | number>(1);
    const { data } = useInfiniteLoading({
        url: `user/coupon?status=${tab}`,
    });

    return <View className={'bg-white min-h-screen flex flex-col'}>
        <Background white>
            <NavBar>
                <PageTitle title={'我的消费券'} />
            </NavBar>
        </Background>
        <Tabs value={tab} onChange={setTab}>
            <Tabs.TabPane value={1} title='待使用' />
            <Tabs.TabPane value={2} title='已使用' />
            <Tabs.TabPane value={3} title='已过期' />
        </Tabs>
        <View className={'p-[15px] bg-[#F6F6F6] flex-1'}>
            {data.length === 0 && <Empty description={'暂无消费券'} />}
            <ScrollView>
                <ListView>
                    {data.map((item) => {
                        return <View className={'rounded-[8px] bg-[#FFFFFF] flex items-center mb-[12px]'} key={item.id}>
                            <View className={'size-[86px] flex justify-center items-center text-[#FF724C] relative'}>
                                <View className={'text-[16px]'}>
                                    ￥
                                    <Text className={'text-[32px] font-semibold'}>
                                        {Number(item.amount).toFixed(0)}
                                    </Text>
                                </View>
                                <View className={'size-[14px] bg-[#F6F6F6] rounded-[50%] bottom-[-7px] right-[-7px] absolute'} />
                                <View className={'size-[14px] bg-[#F6F6F6] rounded-[50%] top-[-7px] right-[-7px] absolute'} />
                            </View>
                            <View className={'w-0 h-[59px] border-l border-dashed border-black opacity-10'} />
                            <View className={'px-[15px] flex-1 flex items-center'}>
                                <View className={'flex-1'}>
                                    <View className={'text-[15px] font-semibold'}>{item.name}</View>
                                    <View className={'text-[12px] text-[#999] mb-[3px]'}>
                                        {dayjs(item.end_time).format('YYYY-MM-DD')}到期
                                    </View>
                                </View>
                                {tab === 1 &&
                                    <Navigator url={`/pages/coupon/detail?id=${item.pivot.id}`} className={'bg-[#FF724C] rounded-[30px] text-[13px] text-white p-[6px_10px]'}>去使用</Navigator>}
                                {tab === 2 &&
                                    <View className={'bg-[#F6F6F6] rounded-[30px] text-[13px] text-[#ccc] p-[6px_10px]'}>已使用</View>}
                                {tab === 3 &&
                                    <View className={'bg-[#F6F6F6] rounded-[30px] text-[13px] text-[#ccc] p-[6px_10px]'}>已过期</View>}
                            </View>
                        </View>;
                    })}
                </ListView>
            </ScrollView>
        </View>

    </View>;
}
