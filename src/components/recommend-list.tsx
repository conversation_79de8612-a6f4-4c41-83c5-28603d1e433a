import { <PERSON>rid<PERSON><PERSON><PERSON>, Image, Navigator, ScrollView, Text, View } from '@tarojs/components';
import likes from '../assets/likes.svg';
import views from '../assets/views.svg';
import useInfiniteLoading from '../hooks/use-infinite-loading';
import getImageUrl from '../utils/get-image-url';

interface RecommendListProps {
  className?: string;
}

export default function RecommendList({
  className
}: RecommendListProps) {
  // 获取推荐列表数据
  const { data = [] } = useInfiniteLoading({
    url: 'eatery',
  });

  return (
    <View className={className}>
      <ScrollView type='custom'>
        <GridView
          className={'px-[15px]'}
          type='masonry'
          cross-axis-count='2'
          cross-axis-gap='10'
          main-axis-gap='10'
        >
          {data.map((item) => (
            <Navigator
              url={`/pages/eatery/detail?id=${item.id}`}
              className={'rounded-[8px] overflow-hidden bg-white flex flex-col break-inside-avoid'}
              key={item.id}
            >
              <Image
                className={'w-full'}
                mode={'widthFix'}
                src={getImageUrl(item.images[0])}
              />
              <View className={'p-[8px] flex flex-col gap-[7px]'}>
                <View className={'text-[13px] font-semibold leading-normal'}>
                  {item.name}
                </View>
                {item.tags.length > 0 && (
                  <View className={'text-[12px] text-[#999999] overflow-hidden text-ellipsis flex gap-[6px]'}>
                    {item.tags.map((tag, idx) => (
                      <Text key={idx} className={'whitespace-nowrap'}>
                        {tag}
                      </Text>
                    ))}
                  </View>
                )}
                <View className={'flex items-center gap-[8px] leading-[18px]'}>
                  <View className={'flex items-center gap-[4px] text-[13px]'}>
                    <Image className={'size-[16px]'} src={views} />
                    {item.views}
                  </View>
                  <View className={'flex items-center gap-[4px] text-[13px]'}>
                    <Image className={'size-[16px]'} src={likes} />
                    {item.likes}
                  </View>
                </View>
              </View>
            </Navigator>
          ))}
        </GridView>
      </ScrollView>
    </View>
  );
}
