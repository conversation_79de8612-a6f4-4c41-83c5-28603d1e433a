import { useRouter } from '@tarojs/taro';
import useRequest from '../../hooks/use-request';
import FoodForm from './components/food-form';

export default function() {
    const { params: { id = '' } } = useRouter();

    // 获取食品详情
    const { result: food } = useRequest(
        { url: `eatery/food/${id}` }
    );

    // 如果还没有获取到食品详情，则不渲染表单
    if (!food) {
        return null;
    }

    // 准备初始数据
    const initialData = {
        cover: food.cover || '',
        name: food.name || '',
        price: food.price ? String(food.price) : '',
    };

    return (
        <FoodForm
            initialData={initialData}
            title="编辑商品"
            saveUrl={`eatery/food/${id}`}
            saveMethod="PUT"
        />
    );
}
