import { Empty } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';
import { useRouter } from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import dayjs from 'dayjs';

export default function() {
    const { params: { id = 0 } } = useRouter();

    const { result: coupon, error } = useRequest({
        url: `/user/coupon/${id}`,
    });

    return <View>
        <Background className={'min-h-screen'}>
            <NavBar>
                <PageTitle title={'核销券码'} />
            </NavBar>
            {error && <View className={'p-[12px]'}><Empty status='error' description='加载失败' /></View>}
            {coupon && <View className={'flex flex-col gap-[10px]'}>
                <View className={'rounded-[12px] mt-[12px] overflow-hidden'}>
                    <View className={'py-[6px] bg-[#FFF1ED] text-[#FF724C] text-[13px] text-center'}>
                        消费券将在{dayjs(coupon.end_time).format('MM月DD日')}到期
                    </View>
                    <View className={'relative h-[350px] px-[20px]'}>
                        <Image className={'absolute size-full top-0 left-0 right-0'} src={require('../../assets/coupon-card.svg')} />
                        <View className={'relative z-1 flex flex-col h-full'}>
                            <View className={'h-[76px] flex items-center justify-center'}>
                                <View className={'text-[20px] text-[#FF724C]'}>
                                    ￥
                                    <Text className={'text-[36px] font-semibold'}>
                                        {coupon.amount}
                                    </Text>
                                </View>
                            </View>
                            <View className={'border-b border-dashed border-black opacity-10 '} />
                            <View className={'flex-1 py-[15px] flex flex-col items-center justify-center'}>
                                <Image className={'size-[200px]'} src={coupon.qrcode} />
                                <View className={'text-[24px] font-semibold'}>{coupon.pivot.code}</View>
                            </View>
                        </View>

                    </View>
                </View>
                {coupon.rules && <View className={'p-[12px] rounded-[12px] bg-white'}>
                    <View className={'flex items-center gap-[8px] mb-[11px]'}>
                        <Image className={'size-[16px]'} src={require('../../assets/ticket.svg')} />
                        <View className={'text-[14px] font-semibold'}>消费券使用规则</View>
                    </View>
                    <View className={'text-[14px] text-[#333] flex flex-col gap-[6px] leading-normal'}>
                        {coupon.rules.split('\n').map((item, index) => {
                            return <View key={index} className={'relative ps-[20px]'}>
                                <View className={'absolute size-[18px] left-0 top-0 text-center'}>•</View>
                                {item}
                            </View>;
                        })}
                    </View>
                </View>}
            </View>}
        </Background>
    </View>;
}
