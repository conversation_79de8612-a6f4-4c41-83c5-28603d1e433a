import { Image, ListView, Navigator, ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import queryString from 'query-string';
import { useState } from 'react';
import Background from '../../components/background';
import FilterSortBar from '../../components/filter-sort-bar';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import SearchBar from '../../components/search-bar';
import SpotItem from '../../components/spot-item';
import useInfiniteLoading from '../../hooks/use-infinite-loading';
import useRequest from '../../hooks/use-request';
import getImageUrl from '../../utils/get-image-url';

export default function() {
    const [filterParams, setFilterParams] = useState({});

    // 处理筛选和排序变化
    const handleFilterChange = (params) => {
        setFilterParams(params);
    };

    // 处理页面点击事件，用于关闭下拉菜单
    const handlePageClick = () => {
        Taro.eventCenter.trigger('page:click');
    };

    const { result: recommendSpots = [] } = useRequest({
        url: 'spot/recommend'
    });

    // 构建 URL
    const url = queryString.stringifyUrl({
        url: 'spot',
        query: filterParams
    });

    const { data } = useInfiniteLoading({
        url,
    });

    return <View className={'bg-white min-h-screen'} onClick={handlePageClick}>
        <Background>
            <NavBar>
                <PageTitle title={'玩乐'} />
            </NavBar>
            <SearchBar placeholder={'搜索景点'} type='spot' />
            <FilterSortBar onFilterChange={handleFilterChange} />
        </Background>
        <View className={'bg-[#F6F6F6] h-[8px]'} />
        {recommendSpots.length > 0 && <>
            <ScrollView scrollX>
                <View className={'p-[15px] flex flex-row gap-[10px]'}>
                    {recommendSpots.map((spot, index) => {
                        return <Navigator url={`/pages/spot/detail?id=${spot.id}`} className={'bg-white flex-shrink-0 relative rounded-[8px] shadow-[0px_4px_15px_0px_rgba(0,0,0,0.08)] w-[136px] overflow-hidden pb-[10px]'}>
                            <Image className={'w-[136px] h-[96px] mb-[8px]'} src={getImageUrl(spot.images[0])} />
                            <View className={'px-[8px] whitespace-nowrap text-ellipsis overflow-hidden text-[14px] font-semibold'}>{spot.name}</View>
                            <View className={'bg-[#F4495C] absolute top-0 left-0 text-white rounded-[8px_0] p-[4px_6px] flex flex-col items-center'}>
                                <View className={'text-[12px]'}>TOP</View>
                                <View className={'text-[17px] font-semibold'}>{String(index + 1)
                                .padStart(2, '0')}</View>
                            </View>
                        </Navigator>;
                    })}
                    <View className={'w-0'}>&nbsp;</View>
                </View>
            </ScrollView>
            <View className={'h-[8px] bg-[#F6F6F6]'} />
        </>}
        <ScrollView>
            <ListView padding={[15, 15, 15, 15]}>
                {data.map((item) => {
                    return <SpotItem spot={item} />;
                })}
            </ListView>
        </ScrollView>
    </View>;
}
