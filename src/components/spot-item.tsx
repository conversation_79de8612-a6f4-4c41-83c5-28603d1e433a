import { Image, Navigator, View, Text } from '@tarojs/components';
import getImageUrl from '../utils/get-image-url';

interface Props {
    spot: Spot;
}

export default function SpotItem({ spot }: Props) {
    return <Navigator url={`/pages/spot/detail?id=${spot.id}`} className={'flex gap-[10px] relative mb-[10px] pb-[10px] after:absolute after:left-[130px] after:right-0 after:bottom-0 after:content-[\'\'] after:border-b after:border-[#f1f1f1]'}>
        <View className={'w-[120px] h-[96px] flex-shrink-0 rounded-[4px] relative overflow-hidden'}>
            <Image className={'size-full'} mode={'aspectFill'} src={getImageUrl(spot.images[0])}  />
        </View>
        <View className={'flex gap-[8px] flex-col flex-1'}>
            <View className={'text-[16px] font-semibold'}>{spot.name}</View>
            <View className={'flex items-center gap-[8px] text-[#666666] text-[13px]'}>
                <View>{spot.views}人浏览</View>
                <View className={'w-[1px] bg-[#cccccc] h-[10px]'} />
                <View>{spot.likes}人点赞</View>
            </View>
            <View className={'text-[12px] text-[#666] flex gap-[4px] items-baseline'}>
                门票
                <View className={'text-[#F4495C]'}>
                    ￥<Text className={'text-[24px] font-semibold'}>{spot.tickets && spot.tickets.length > 0 ? Math.min(...spot.tickets.map(ticket => ticket.price)) : '--'}</Text>
                </View>
                起
            </View>
        </View>
    </Navigator>;
}
