import { Empty } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import EateryItem from '../../components/eatery-item';
import HotelItem from '../../components/hotel-item';
import SpotItem from '../../components/spot-item';
import useInfiniteLoading from '../../hooks/use-infinite-loading';

interface SearchResultsProps {
    keyword: string;
    category: string;
}

export default function SearchResults({ keyword, category }: SearchResultsProps) {
    // 只有在有关键词时才会调用搜索接口
    const { data: searchResults = [], loading } = useInfiniteLoading({
        url: `search/${category}?keyword=${encodeURIComponent(keyword)}`,
    });

    if (loading) {
        return (
            <View className={'flex justify-center items-center p-[30px]'}>
                <View className={'text-[14px] text-[#999999]'}>正在搜索...</View>
            </View>
        );
    }

    if (searchResults.length === 0) {
        return <Empty description={'没有找到相关结果'} />;
    }

    return (
        <View className={'px-[15px]'}>
            {searchResults.map((item) => {
                switch (category) {
                    case 'hotel':
                        return <HotelItem key={item.id} hotel={item} />;
                    case 'spot':
                        return <SpotItem key={item.id} spot={item} />;
                    case 'eatery':
                    default:
                        return <EateryItem key={item.id} eatery={item} />;
                }
            })}
        </View>
    );
}
