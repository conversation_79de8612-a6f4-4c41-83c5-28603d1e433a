import {View, Image} from '@tarojs/components';
import {useState} from 'react';
import clsx from 'clsx';
import Dropdown, {DropdownOption} from './dropdown';
import downIcon from '../assets/down.svg';
import sortIcon from '../assets/sort.svg';
import sortAscIcon from '../assets/sort-asc.svg';
import sortDescIcon from '../assets/sort-desc.svg';

// 区县选项
const districtOptions: DropdownOption[] = [
    {value: '全部', label: '全部'},
    {value: '利通区', label: '利通区'},
    {value: '青铜峡市', label: '青铜峡市'},
    {value: '红寺堡区', label: '红寺堡区'},
    {value: '盐池县', label: '盐池县'},
    {value: '同心县', label: '同心县'},
];

// 排序方式枚举
export enum SortMode {
    DESC = 'desc',     // 只支持降序
    BOTH = 'both'      // 支持升序和降序
}

// 排序选项接口
export interface SortOption {
    field: string;     // 排序字段
    label: string;     // 显示名称
    mode?: SortMode;    // 排序方式，默认为 DESC
}

// 默认排序选项
const defaultSortOptions: SortOption[] = [
    {field: 'likes', label: '点赞数'},
    {field: 'views', label: '浏览数'}
];

interface FilterSortBarProps {
    onFilterChange: (params: { district?: string; sort?: string; order?: string }) => void;
    className?: string;
    sortOptions?: SortOption[];
}

export default function FilterSortBar(
    {
        onFilterChange,
        className = '',
        sortOptions = defaultSortOptions
    }: FilterSortBarProps
) {
    const [district, setDistrict] = useState('全部');
    const [sortBy, setSortBy] = useState('');
    const [sortOrder, setSortOrder] = useState('asc');

    // 处理区域变化
    const handleDistrictChange = (value: string) => {
        setDistrict(value);
        onFilterChange({
            district: value !== '全部' ? value : undefined,
            sort: sortBy || undefined,
            order: sortBy ? sortOrder : undefined
        });
    };

    // 处理排序点击
    const handleSortClick = (option: SortOption) => {
        const {field, mode = SortMode.DESC} = option;

        // 如果点击的是当前排序字段
        if (sortBy === field) {
            // 如果支持双向排序，在正序、逆序和不排序之间循环
            if (mode === SortMode.BOTH) {
                if (sortOrder === 'asc') {
                    // 如果当前是升序，切换到降序
                    setSortOrder('desc');
                    onFilterChange({
                        district: district !== '全部' ? district : undefined,
                        sort: field,
                        order: 'desc'
                    });
                } else {
                    // 如果当前是降序，取消排序
                    setSortBy('');
                    setSortOrder('asc'); // 重置为默认值
                    onFilterChange({
                        district: district !== '全部' ? district : undefined,
                        sort: undefined,
                        order: undefined
                    });
                }
            } else {
                // 如果不支持双向排序，取消排序
                setSortBy('');
                setSortOrder('asc'); // 重置为默认值
                onFilterChange({
                    district: district !== '全部' ? district : undefined,
                    sort: undefined,
                    order: undefined
                });
            }
        } else {
            // 如果点击的是新的排序字段
            setSortBy(field);
            // 设置默认排序顺序，支持双向排序的字段默认使用升序，其他字段默认使用降序
            const newOrder = mode === SortMode.BOTH ? 'asc' : 'desc';
            setSortOrder(newOrder);
            onFilterChange({
                district: district !== '全部' ? district : undefined,
                sort: field,
                order: newOrder
            });
        }
    };

    // 渲染排序图标
    const renderSortIcon = (option: SortOption) => {
        const {field, mode = SortMode.DESC} = option;
        const isActive = sortBy === field;
        const isAscending = isActive && mode === SortMode.BOTH && sortOrder === 'asc';

        // 根据排序模式和状态选择不同的图标
        if (mode === SortMode.BOTH) {
            // 双向排序根据当前排序状态选择不同的图标
            if (!isActive) {
                // 未选中状态使用默认的双向箭头图标
                return <Image
                    src={sortIcon}
                    className="w-[16px] h-[16px] self-center"
                />;
            } else if (isAscending) {
                // 升序状态使用上半部分高亮的图标
                return <Image
                    src={sortAscIcon}
                    className="w-[16px] h-[16px] self-center"
                />;
            } else {
                // 降序状态使用下半部分高亮的图标
                return <Image
                    src={sortDescIcon}
                    className="w-[16px] h-[16px] self-center"
                />;
            }
        } else {
            // 单向排序使用向下箭头图标
            return <Image
                src={downIcon}
                className={clsx(
                    'w-[16px] h-[16px] self-center',
                    isActive ? 'opacity-100' : 'opacity-50'
                )}
                style={{
                    filter: isActive ? 'invert(60%) sepia(75%) saturate(456%) hue-rotate(140deg) brightness(86%) contrast(84%)' : 'none'
                }}
            />;
        }
    };

    return (
        <View className={`flex justify-between p-[10px] text-[14px] text-[#333] ${className}`}>
            <View className='w-[80px] flex justify-start'>
                <Dropdown
                    options={districtOptions}
                    value={district}
                    onChange={handleDistrictChange}
                    triggerId='district-dropdown-trigger'
                    className='relative'
                    triggerClassName='flex items-center gap-[6px]'
                    offsetX={-10}
                />
            </View>

            {/* 排序选项 */}
            {sortOptions.map((option, index) => {
                // 根据索引决定对齐方式，第一个居中，第二个右对齐
                const justifyClass = index === 0 ? 'justify-center' : 'justify-end';

                return (
                    <View
                        key={option.field}
                        className={`w-[80px] flex ${justifyClass} items-center gap-[6px]`}
                        onClick={() => handleSortClick(option)}
                    >
                        <View>{option.label}</View>
                        {renderSortIcon(option)}
                    </View>
                );
            })}
        </View>
    );
}
