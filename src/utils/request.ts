import Taro from '@tarojs/taro';
import { navigateToLogin } from './auth';

export interface Option extends Taro.request.Option {
    file?: string;
    toast?: boolean;
    checkLogin?: boolean;
    onMessage?: (message: string) => void;
}

// 二进制转字符串工具
const arrayBufferToString = (buffer: ArrayBuffer) => {
    if (typeof TextDecoder !== 'undefined') {
        return new TextDecoder('utf-8').decode(buffer);
    }
    let str = '';
    new Uint8Array(buffer).forEach((byte) => {
        str += String.fromCharCode(byte);
    });
    return str;
};

export default async function request({ file, toast = false, checkLogin = false, onMessage, ...option }: Option) {
    try {
        if (toast) {
            Taro.showLoading();
        }
        option.url = `${HOST}/api/${option.url}`;

        const user = Taro.getStorageSync('user');
        if (user) {
            option.header = {
                ...option.header,
                Authorization: `Bearer ${user.token}`,
            };
        }
        if (onMessage) {
            option.enableChunked = true;
        }
        let result: Taro.request.SuccessCallbackResult | Taro.uploadFile.SuccessCallbackResult;
        if (file) {
            result = await Taro.uploadFile({
                ...option,
                filePath: file,
                name: 'file',
                formData: option.data,
            });
            result.data = JSON.parse(result.data);
        } else {
            const task = Taro.request(option);

            if (onMessage) {
                let buffer = '';
                task.onChunkReceived(({ data }) => {
                    const textChunk = arrayBufferToString(data);
                    buffer = processStreamData(buffer + textChunk);
                });

                const processStreamData = (rawData: string) => {
                    let remainingData = rawData;
                    while (true) {
                        const eventEndIndex = remainingData.indexOf('\n\n');
                        if (eventEndIndex === -1) break;

                        const eventData = remainingData.slice(0, eventEndIndex);
                        remainingData = remainingData.slice(eventEndIndex + 2);

                        const match = eventData.match(/^data:(.*)/);
                        if (match) {
                            const message = match[1].trim();
                            if (message && message !== '[DONE]') {
                                onMessage(message);
                            }
                        }
                    }
                    return remainingData;
                };
            }
            result = await task;
        }

        if (toast) {
            Taro.hideLoading();
        }

        if (result.statusCode < 200 || result.statusCode >= 300) {
            if (result.statusCode === 401 && checkLogin) {
                navigateToLogin();
            } else if (result.statusCode === 422) {
                if (typeof result.data === 'string') {
                    throw new Error(result.data);
                } else {
                    const message = Object.entries(result.data).map(([, value]) => value).join('\n');
                    throw new Error(message);
                }
            } else if (typeof result.data === 'object' && 'message' in result.data) {
                throw new Error(result.data.message);
            }

            throw new Error('网络错误');
        }
        return result.data;
    } catch (e) {
        if (toast) {
            Taro.hideLoading();
            Taro.showToast({
                title: e.message || '未知错误',
                icon: 'none',
            });
        }
        throw e;
    }
}
