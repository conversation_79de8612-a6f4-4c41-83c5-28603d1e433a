/**
 * 判断餐厅是否在营业中
 * @param hours 餐厅营业时间，格式为 { start: "HH:MM", end: "HH:MM" }
 * @returns 返回餐厅是否在营业中
 */
export default function isOpen(hours: { start: string, end: string }): boolean {
  // 如果没有营业时间信息，默认为不营业
  if (!hours || !hours.start || !hours.end) {
    return false;
  }

  // 获取当前时间
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // 解析营业开始时间
  const [startHour, startMinute] = hours.start.split(':').map(Number);

  // 解析营业结束时间
  const [endHour, endMinute] = hours.end.split(':').map(Number);

  // 将时间转换为分钟数，便于比较
  const currentTimeInMinutes = currentHour * 60 + currentMinute;
  const startTimeInMinutes = startHour * 60 + startMinute;
  const endTimeInMinutes = endHour * 60 + endMinute;

  // 如果当前时间在开始时间和结束时间之间，则在营业中
  return currentTimeInMinutes >= startTimeInMinutes && currentTimeInMinutes <= endTimeInMinutes;
}
