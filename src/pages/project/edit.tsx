import { useRouter } from '@tarojs/taro';
import useRequest from '../../hooks/use-request';
import ProjectForm from './components/project-form';

export default function() {
    const { params: { id = '' } } = useRouter();

    // 获取项目详情
    const { result: project } = useRequest(
        { url: `spot/project/${id}` }
    );

    // 如果还没有获取到项目详情，则不渲染表单
    if (!project) {
        return null;
    }

    // 准备初始数据
    const initialData = {
        cover: project.cover || '',
        name: project.name || '',
        price: project.price ? String(project.price) : '',
    };

    return (
        <ProjectForm
            initialData={initialData}
            title='编辑项目'
            saveUrl={`spot/project/${id}`}
            saveMethod='PUT'
        />
    );
}
