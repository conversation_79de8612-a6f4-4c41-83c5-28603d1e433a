{"miniprogramRoot": "dist/", "projectname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "塞上早茶", "appid": "wx65f633c23e85a38b", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "minifyWXML": false, "minifyWXSS": false}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.6.5", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}