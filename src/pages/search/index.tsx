import { SafeArea } from '@nutui/nutui-react-taro';
import { Image, Input, View } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import SearchIcon from '../../assets/search.svg';
import Background from '../../components/background';
import Dropdown, { DropdownOption } from '../../components/dropdown';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import RecommendList from '../../components/recommend-list';
import SearchResults from './search-results';

export default function() {
    const router = useRouter();
    const [keyword, setKeyword] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [historySearches, setHistorySearches] = useState<string[]>([]);
    const [category, setCategory] = useState(router.params.type || 'eatery');

    // 当类别变化时，如果有搜索关键词，自动更新搜索结果
    const handleCategoryChange = (newCategory: string) => {
        setCategory(newCategory);
    };

    // 搜索类别选项
    const categoryOptions: DropdownOption[] = [
        { value: 'eatery', label: '商户' },
        { value: 'spot', label: '景点' },
        { value: 'hotel', label: '酒店' },
    ];

    // 不再在这里调用搜索接口和推荐列表接口，而是在子组件中调用

    // 加载历史搜索记录
    useEffect(() => {
        const history = Taro.getStorageSync('searchHistory');
        if (history) {
            setHistorySearches(history);
        }
    }, []);

    // 保存搜索历史
    const saveSearchHistory = (term: string) => {
        if (!term.trim()) return;

        const newHistory = [term, ...historySearches.filter(item => item !== term)];
        // 只保留最新10条记录
        const limitedHistory = newHistory.slice(0, 10);
        setHistorySearches(limitedHistory);
        Taro.setStorageSync('searchHistory', limitedHistory);
    };

    // 清除历史记录
    const clearHistory = () => {
        setHistorySearches([]);
        Taro.removeStorageSync('searchHistory');
    };

    const handleSearch = () => {
        if (keyword.trim()) {
            const term = keyword.trim();
            setSearchTerm(term);
            saveSearchHistory(term);
            // URL变化时会自动重新加载数据
        } else {
            // 如果关键词为空，清空搜索词，显示推荐列表
            setSearchTerm('');
        }
    };

    const handleHistoryItemClick = (term: string) => {
        setKeyword(term);
        setSearchTerm(term);
        saveSearchHistory(term);
        // URL变化时会自动重新加载数据
    };

    const handleKeywordChange = (e) => {
        setKeyword(e.detail.value);
    };

    // 点击页面时触发事件，用于关闭下拉菜单
    const handlePageClick = () => {
        Taro.eventCenter.trigger('page:click');
    };

    return <View className={'min-h-screen bg-white'} onClick={handlePageClick}>
        <Background white>
            <NavBar>
                <PageTitle title={'搜索'} />
            </NavBar>
            <View className={'border-2 border-[rgba(18,18,18,1)] flex justify-between items-center rounded-[20px] p-[4px_4px_4px_0] my-[5px] mb-[6px] bg-white'}>
                <Dropdown
                    options={categoryOptions}
                    value={category}
                    onChange={handleCategoryChange}
                    triggerId="category-dropdown-trigger"
                    className="relative ms-[8px]"
                    triggerClassName="flex items-center gap-[6px] px-[8px] py-[4px] rounded-[16px]"
                    offsetX={-10}
                />
                <View className={'w-[1px] h-[12px] bg-[#666]'} />
                <Input
                    className={'flex-1 px-[8px] text-[13px]'}
                    type='text'
                    placeholder={`搜索${categoryOptions.find(opt => opt.value === category)?.label || '商户'}`}
                    value={keyword}
                    onInput={handleKeywordChange}
                    onConfirm={handleSearch}
                    focus={true}
                />
                <View
                    className={'flex items-center justify-center rounded-[22px] p-[6px_16px] bg-[rgba(18,18,18,1)]'}
                    onClick={handleSearch}
                >
                    <Image className={'w-[16px] h-[16px]'} src={SearchIcon} />
                </View>
            </View>
        </Background>
        {/* 历史搜索记录 */}
        {!searchTerm && historySearches.length > 0 && (
            <View className={'p-[15px]'}>
                <View className={'flex justify-between items-center mb-[10px]'}>
                    <View className={'text-[15px] font-semibold'}>历史搜索</View>
                    <View
                        className={'text-[13px] text-[#999999]'}
                        onClick={clearHistory}
                    >
                        清除
                    </View>
                </View>
                <View className={'flex flex-wrap gap-[10px]'}>
                    {historySearches.map((term, index) => (
                        <View
                            key={index}
                            className={'bg-[#F6F6F6] rounded-[16px] px-[12px] py-[6px] text-[13px]'}
                            onClick={() => handleHistoryItemClick(term)}
                        >
                            {term}
                        </View>
                    ))}
                </View>
            </View>
        )}
        <View className={'pt-[12px]'}>
            {/* 搜索结果 - 只有在有搜索关键词时才渲染子组件 */}
            {searchTerm ? (
                <SearchResults key={category} keyword={searchTerm} category={category} />
            ) : (
                /* 推荐列表 - 与首页相同 */
                <RecommendList />
            )}
        </View>
        <SafeArea position={'bottom'} />
    </View>;
}
