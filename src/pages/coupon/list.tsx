import { Empty } from '@nutui/nutui-react-taro';
import { <PERSON><PERSON><PERSON><PERSON>, Navigator, Sc<PERSON>View, Text, View } from '@tarojs/components';
import dayjs from 'dayjs';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import useUser from '../../hooks/use-user';

export default function() {

    const user = useUser();

    if (!user) {
        return null;
    }

    const { result = [] } = useRequest({
        url: `coupon`,
    });

    return <View className={'bg-white min-h-screen flex flex-col'}>
        <Background white>
            <NavBar>
                <PageTitle title={'领取消费券'} />
            </NavBar>
        </Background>
        <View className={'p-[15px] bg-[#F6F6F6] flex-1'}>
            {result.length === 0 && <Empty description={'暂无消费券'} />}
            <ScrollView>
                <ListView>
                    {result.map((item) => {
                        return <View className={'rounded-[8px] bg-[#FFFFFF] flex items-center mb-[12px]'} key={item.id}>
                            <View className={'size-[86px] flex justify-center items-center text-[#FF724C] relative'}>
                                <View className={'text-[16px]'}>
                                    ￥
                                    <Text className={'text-[32px] font-semibold'}>
                                        {Number(item.amount).toFixed(0)}
                                    </Text>
                                </View>
                                <View className={'size-[14px] bg-[#F6F6F6] rounded-[50%] bottom-[-7px] right-[-7px] absolute'} />
                                <View className={'size-[14px] bg-[#F6F6F6] rounded-[50%] top-[-7px] right-[-7px] absolute'} />
                            </View>
                            <View className={'w-0 h-[59px] border-l border-dashed border-black opacity-10'} />
                            <View className={'px-[15px] flex-1 flex items-center'}>
                                <View className={'flex-1'}>
                                    <View className={'text-[15px] font-semibold'}>{item.name}</View>
                                    <View className={'text-[12px] text-[#999] mb-[3px]'}>
                                        {dayjs(item.end_time).format('YYYY-MM-DD')}到期
                                    </View>
                                </View>
                                <Navigator url={`/pages/coupon/claim?id=${item.id}`} className={'bg-[#FF724C] rounded-[30px] text-[13px] text-[#fff] p-[6px_10px]'}>去领取</Navigator>
                            </View>
                        </View>;
                    })}
                </ListView>
            </ScrollView>
        </View>

    </View>;
}
