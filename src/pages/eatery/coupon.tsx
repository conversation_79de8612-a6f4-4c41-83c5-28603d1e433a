import { Empty } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useInfiniteLoading from '../../hooks/use-infinite-loading';

export default function() {
    const { data } = useInfiniteLoading({
        url: 'eatery/coupon'
    });

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'核销数据明细'} />
            </NavBar>
        </Background>
        <View className={'p-[15px]'}>
            <View className={'bg-white p-[16px_12px] rounded-[8px]'}>
                {data.length === 0 && <Empty description={'暂无核销记录'} />}
                {data.map((item, index) => {
                    return <>
                        {index > 0 && <View className={'h-[1px] bg-[#F1F1F1] my-[16px]'} />}
                        <View className={'flex justify-between'}>
                            <View>
                                <View className={'text-[15px] font-semibold'}>{item.info.name}-{item.code}</View>
                                <View className={'text-[13px] text-[#999] mt-[6px]'}>核销时间: {item.use_time}</View>
                            </View>
                            <View className={'text-[20px] text-[#FF724C] font-semibold'}>￥{item.info.amount}</View>
                        </View>
                    </>;
                })}
            </View>
        </View>
    </View>;
}
