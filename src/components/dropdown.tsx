import { View, Text, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import dropdownIcon from '../assets/dropdown.svg';

export interface DropdownOption {
    value: string;
    label: string;
}

interface DropdownProps {
    options: DropdownOption[];
    value: string;
    onChange: (value: string) => void;
    className?: string;
    triggerClassName?: string;
    optionClassName?: string;
    selectedClassName?: string;
    dropdownClassName?: string;
    placeholder?: string;
    offsetX?: number;
    offsetY?: number;
    triggerId?: string;
}

export default function Dropdown({
    options,
    value,
    onChange,
    className = '',
    triggerClassName = '',
    optionClassName = 'p-[8px_20px] text-[13px]',
    selectedClassName = 'text-[#58B4BF] font-semibold',
    dropdownClassName = 'fixed bg-white rounded-[8px] shadow-md z-50',
    placeholder = '请选择',
    offsetX = 0,
    offsetY = 2,
    triggerId = 'dropdown-trigger'
}: DropdownProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [position, setPosition] = useState({ top: 0, left: 0 });

    const selectedOption = options.find(option => option.value === value) || options[0];

    const handleToggle = (e) => {
        // 阻止事件冒泡，避免触发页面点击事件
        e.stopPropagation();

        // 如果已经打开，则关闭
        if (isOpen) {
            setIsOpen(false);
            return;
        }

        // 获取触发元素的位置信息
        const query = Taro.createSelectorQuery();
        query.select(`#${triggerId}`).boundingClientRect();
        query.exec((res) => {
            if (res && res[0]) {
                const rect = res[0];
                setPosition({
                    top: rect.top + rect.height + offsetY,
                    left: rect.left + offsetX
                });
                setIsOpen(true);
            }
        });
    };

    const handleSelect = (e, option: DropdownOption) => {
        // 阻止事件冒泡
        e.stopPropagation();

        onChange(option.value);
        setIsOpen(false);
    };

    // 点击其他区域关闭下拉菜单
    useEffect(() => {
        if (isOpen) {
            const handleClick = () => {
                setIsOpen(false);
            };

            // 延迟添加事件监听，避免触发元素的点击事件同时触发关闭
            const timer = setTimeout(() => {
                Taro.eventCenter.on('page:click', handleClick);
            }, 100);

            return () => {
                clearTimeout(timer);
                Taro.eventCenter.off('page:click', handleClick);
            };
        }
    }, [isOpen]);

    return (
        <View className={className}>
            <View
                id={triggerId}
                className={triggerClassName}
                onClick={handleToggle}
            >
                <Text className="text-[14px]">{selectedOption?.label || placeholder}</Text>
                <Image src={dropdownIcon} className="w-[16px] h-[16px] self-center" />
            </View>

            {isOpen && (
                <View
                    className={dropdownClassName}
                    style={{
                        top: `${position.top}px`,
                        left: `${position.left}px`,
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    {options.map((option) => (
                        <View
                            key={option.value}
                            className={`${optionClassName} ${option.value === value ? selectedClassName : ''}`}
                            onClick={(e) => handleSelect(e, option)}
                        >
                            {option.label}
                        </View>
                    ))}
                </View>
            )}
        </View>
    );
}
