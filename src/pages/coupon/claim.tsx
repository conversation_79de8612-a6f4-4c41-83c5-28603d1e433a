import { Empty, SafeArea } from '@nutui/nutui-react-taro';
import { Image, View, Text } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import request from '../../utils/request';

export default function() {

    const { params: { id = 0 } } = useRouter();

    const { result: coupon, error } = useRequest({
        url: `coupon/${id}`,
    });

    return <View className={'min-h-screen'}>
        <Background white>
            <NavBar>
                <PageTitle title={'领取消费券'} />
            </NavBar>
        </Background>
        {error && <View className={'p-[12px]'}><Empty status='error' description='加载失败' /></View>}
        {coupon && <View className={'relative'}>
            <Image className={'w-full absolute'} mode={'widthFix'} src={require('../../assets/coupon-bg.png')} />
            <View className={'relative z-1 flex flex-col items-center px-[15px] pt-[231px]'}>
                <View className={'w-full relative'} onClick={async () => {
                    await request({
                        url: `coupon/${id}/claim`,
                        method: 'POST',
                        toast: true
                    });
                    await Taro.showToast({
                        title: '领取成功',
                        icon: 'success',
                        duration: 1500
                    });
                    setTimeout(() => {
                        Taro.navigateBack();
                    }, 1500);
                }}>
                    <Image className={'w-full'} mode={'widthFix'} src={require('../../assets/coupon-claim.png')} />
                    <View className={'w-full absolute top-0 pt-[22px]'}>
                        <View className={'px-[22px] text-[#121212] text-[15px] font-semibold text-center'}>{coupon.name}</View>
                        <View className={'text-center text-[#FF724C]'}>
                            ￥<Text className={'text-[32px]'}>{coupon.amount}</Text>
                        </View>
                    </View>
                </View>
                {coupon.rules &&
                    <View className={'p-[16px] rounded-[12px] bg-[#FFFFFF] w-full flex flex-col gap-[16px] mt-[15px]'}>
                        <View className={'flex items-center p-[2px_8px] gap-[12px] justify-center'}>
                            <View className={'w-[54px] h-[2px] bg-gradient-to-r from-[rgba(159,88,64,0.00)] to-[#9F5840]'} />
                            <View className={'text-[#9F5840] font-semibold'}>消费券使用规则</View>
                            <View className={'w-[54px] h-[2px] bg-gradient-to-r from-[#9F5840] to-[rgba(159,88,64,0.00)]'} />
                        </View>
                        <View className={'text-[14px] text-[#9F5840] flex flex-col gap-[6px] leading-normal'}>
                            {coupon.rules.split('\n').map((item, index) => {
                                return <View key={index} className={'relative ps-[20px]'}>
                                    <View className={'absolute size-[18px] left-0 top-0 text-center'}>•</View>
                                    {item}
                                </View>;
                            })}
                        </View>
                    </View>}
            </View>
        </View>}
        <SafeArea position={'bottom'} />
    </View>;
}
