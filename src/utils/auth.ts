import Taro from '@tarojs/taro';
import queryString from 'query-string';

type PageInfo = {
    path: string;
    params: Record<string, any>;
};

// 获取当前页面路径和参数
function getCurrentPageInfo(): PageInfo {
    // 获取当前页面路由信息
    const pages = Taro.getCurrentPages();
    const currentPage = pages[pages.length - 1];

    if (!currentPage) {
        return { path: '', params: {} };
    }

    // 获取路径
    const path = `/${currentPage.route}`;

    // 获取参数，过滤掉Taro内部参数
    const params = { ...currentPage.options };
    if ('$taroTimestamp' in params) {
        delete params.$taroTimestamp;
    }

    return { path, params };
}

// 跳转到登录页面（替换当前页面）
export function redirectToLogin(): void {
    const { path, params } = getCurrentPageInfo();
    let loginUrl = '/pages/login/index';

    if (path) {
        const redirect = queryString.stringifyUrl({ url: path, query: params });
        loginUrl = `${loginUrl}?redirect=${encodeURIComponent(redirect)}`;
    }

    Taro.redirectTo({
        url: loginUrl
    });
}

// 导航到登录页面（保留当前页面）
export function navigateToLogin(): void {
    Taro.navigateTo({
        url: '/pages/login/index'
    });
}
