{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setting": {"compileHotReLoad": false, "bigPackageSizeSupport": true, "skylineRenderEnable": false, "urlCheck": true}, "condition": {"miniprogram": {"list": [{"name": "pages/coupon/redeem", "pathName": "pages/coupon/redeem", "query": "code=kx5odzld", "scene": null, "launchMode": "default"}, {"name": "pages/coupon/claim", "pathName": "pages/coupon/claim", "query": "id=13", "launchMode": "default", "scene": null}, {"name": "pages/coupon/claim", "pathName": "pages/coupon/claim", "query": "id=2", "launchMode": "default", "scene": null}, {"name": "pages/coupon/list", "pathName": "pages/coupon/list", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/reserve/index", "pathName": "pages/reserve/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/eatery/detail", "pathName": "pages/eatery/detail", "query": "id=22", "launchMode": "default", "scene": null}, {"name": "pages/login/agreement", "pathName": "pages/login/agreement", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/eatery/create", "pathName": "pages/eatery/create", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/hotel/index", "pathName": "pages/hotel/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/spot/detail", "pathName": "pages/spot/detail", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "pages/spot/index", "pathName": "pages/spot/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/chat/index", "pathName": "pages/chat/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/search/index", "pathName": "pages/search/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/eatery/coupon", "pathName": "pages/eatery/coupon", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/coupon/detail", "pathName": "pages/coupon/detail", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "pages/coupon/detail", "pathName": "pages/coupon/detail", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/user/coupon", "pathName": "pages/user/coupon", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/user/star", "pathName": "pages/user/star", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/coupon/claim", "pathName": "pages/coupon/claim", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "pages/user/star", "pathName": "pages/user/star", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/eatery/index", "pathName": "pages/eatery/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/food/create", "pathName": "pages/food/create", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/food/index", "pathName": "pages/food/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/member/index", "pathName": "pages/member/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/login/index", "pathName": "pages/login/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/user/index", "pathName": "pages/user/index", "query": "", "launchMode": "default", "scene": null}]}}, "libVersion": "3.6.6"}