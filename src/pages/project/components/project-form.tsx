import { Input, SafeArea } from '@nutui/nutui-react-taro';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useImmer } from 'use-immer';
import Background from '../../../components/background';
import NavBar from '../../../components/nav-bar';
import PageTitle from '../../../components/page-title';
import getImageUrl from '../../../utils/get-image-url';
import request from '../../../utils/request';

interface ProjectFormProps {
  // 初始数据
  initialData?: {
    cover: string;
    name: string;
    price: string;
  };
  // 页面标题
  title: string;
  // 保存按钮文本
  saveButtonText?: string;
  // 保存请求的URL
  saveUrl: string;
  // 保存请求的方法
  saveMethod: 'POST' | 'PUT';
  // 保存成功后的回调
  onSaveSuccess?: () => void;
}

export default function ProjectForm({
  initialData = { cover: '', name: '', price: '' },
  title,
  saveButtonText = '保存',
  saveUrl,
  saveMethod,
  onSaveSuccess
}: ProjectFormProps) {
  const [data, setData] = useImmer(initialData);

  const handleSave = async () => {
    await request({
      url: saveUrl,
      method: saveMethod,
      toast: true,
      data,
    });

    if (onSaveSuccess) {
      onSaveSuccess();
    } else {
      Taro.navigateBack();
    }
  };

  return (
    <View>
      <Background white>
        <NavBar>
          <PageTitle title={title} />
        </NavBar>
      </Background>
      <View className={'p-[12px] pb-[70px]'}>
        <View className={'flex flex-col gap-[12px]'}>
          <View className={'px-[12px] py-[16px] rounded-xl flex flex-col gap-[16px] bg-white'}>
            <View className={'flex items-center justify-between text-[15px]'}>
              <View className={'font-semibold'}>项目图片</View>
              {data.cover && (
                <View className={'relative bg-gray-100 flex items-center justify-center rounded-[8px] overflow-hidden size-[80px]'}>
                  <Image mode={'aspectFill'} src={getImageUrl(data.cover)} className={'size-[100%]'} />
                  <View
                    className={'bg-[#00000099] flex items-center justify-center size-[22px] rounded-bl-[8px] absolute right-0 top-0'}
                    onClick={() => {
                      setData(draft => {
                        draft.cover = '';
                      });
                    }}
                  >
                    <Image className={'size-[12px]'} src={require('../../../assets/close.svg')} />
                  </View>
                </View>
              )}
              {!data.cover && (
                <View
                  className={'bg-gray-100 flex items-center justify-center rounded-[8px] size-[80px]'}
                  onClick={async () => {
                    const { tempFilePaths } = await Taro.chooseImage({
                      count: 1,
                      sizeType: ['compressed'],
                      sourceType: ['album', 'camera'],
                    });
                    const result = await request({
                      url: 'upload',
                      file: tempFilePaths[0],
                      toast: true,
                    });
                    setData(draft => {
                      draft.cover = result.path;
                    });
                  }}
                >
                  <Image className={'size-[24px]'} src={require('../../../assets/plus.svg')} />
                </View>
              )}
            </View>
            <View className={'h-[1px] bg-[#F1F1F1]'} />
            <View className={'flex items-center justify-between text-[15px]'}>
              <View className={'font-semibold'}>项目名称</View>
              <Input
                align={'right'}
                value={data.name}
                onChange={value =>
                  setData(draft => {
                    draft.name = value;
                  })
                }
                placeholder={'请输入项目名称'}
              />
            </View>
            <View className={'h-[1px] bg-[#F1F1F1]'} />
            <View className={'flex items-center justify-between text-[15px] gap-2'}>
              <View className={'font-semibold'}>项目价格</View>
              <Input
                type={'digit'}
                value={data.price}
                onChange={value =>
                  setData(draft => {
                    draft.price = value;
                  })
                }
                align={'right'}
                placeholder={'请输入'}
              />
              <Text>元</Text>
            </View>
          </View>
        </View>
        <View className={'fixed bottom-0 left-0 right-0 bg-white'}>
          <View className={'px-3 py-2 flex'}>
            <View
              onClick={handleSave}
              className={'p-[10px] grow text-center rounded-3xl bg-[#58B4BF] text-white text-[17px] font-semibold'}
            >
              {saveButtonText}
            </View>
          </View>
          <SafeArea position={'bottom'} />
        </View>
      </View>
      <SafeArea position={'bottom'} />
    </View>
  );
}
