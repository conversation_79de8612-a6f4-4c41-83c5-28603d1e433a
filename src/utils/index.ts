import getImageUrl from './get-image-url';
import isOpen from './is-open';
import { redirectToLogin, navigateToLogin } from './auth';

export function formatCurrency(value: number, digits: number = 0) {
    return Number(value).toLocaleString('zh-CN', {
        style: 'decimal',
        currency: 'CNY',
        minimumFractionDigits: digits
    });
}

export function getAvatar(src?: string) {
    if (!src) {
        return require('../assets/avatar.png');
    }
    return getImageUrl(src);
}

export function isPaginationObject<T>(data: any): data is PaginationType<T> {
    return 'current_page' in data;
}

export { isOpen, redirectToLogin, navigateToLogin };
