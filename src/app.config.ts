export default defineAppConfig({
    pages: [
        'pages/index/index',
        'pages/eatery/index',
        'pages/eatery/detail',
        'pages/eatery/create',
        'pages/eatery/coupon',
        'pages/eatery/member',
        'pages/spot/index',
        'pages/spot/detail',
        'pages/spot/create',
        'pages/spot/coupon',
        'pages/spot/member',
        'pages/hotel/index',
        'pages/hotel/create',
        'pages/hotel/coupon',
        'pages/hotel/member',
        'pages/user/index',
        'pages/user/star',
        'pages/user/coupon',
        'pages/login/index',
        'pages/login/agreement',
        'pages/food/index',
        'pages/food/create',
        'pages/food/edit',
        'pages/project/index',
        'pages/project/create',
        'pages/project/edit',
        'pages/reserve/index',
        'pages/coupon/list',
        'pages/coupon/claim',
        'pages/coupon/detail',
        'pages/coupon/redeem',
        'pages/search/index',
        'pages/chat/index'
    ],
    window: {
        backgroundTextStyle: 'light',
        navigationStyle: 'custom',
        navigationBarTextStyle: 'black',
        navigationBarTitleText: '塞上早茶'
    },
    requiredPrivateInfos: [
        'getLocation',
        'chooseLocation'
    ],
    permission: {
        'scope.userLocation': {
            'desc': '你的位置信息将用于小程序位置接口的效果展示'
        }
    },
    'plugins': {
        'QCloudAIVoice': {
            'version': '2.3.12',
            'provider': 'wx3e17776051baf153'
        }
    }
});
