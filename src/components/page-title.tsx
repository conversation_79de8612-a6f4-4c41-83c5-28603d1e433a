import { Image, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import back from '../assets/back.svg';
import home from '../assets/home.svg';

interface Props {
    title?: string;
}

export default function PageTitle({ title = '' }: Props) {
    const pages = Taro.getCurrentPages();
    const canBack = pages.length > 1;
    return <View className={'flex items-center flex-1 relative'}>
        <Image className={'absolute size-[20px]'} src={canBack ? back : home} onClick={() => canBack ? Taro.navigateBack() : Taro.redirectTo({ url: '/pages/index/index' })} />
        <View className={'px-[40px] flex flex-1 items-center justify-center text-[17px] font-semibold'}>{title}</View>
    </View>;
}
