import { SafeArea } from '@nutui/nutui-react-taro';
import { ListView, ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import queryString from 'query-string';
import Background from '../../components/background';
import FilterSortBar, { SortMode } from '../../components/filter-sort-bar';
import HotelItem from '../../components/hotel-item';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import SearchBar from '../../components/search-bar';
import useInfiniteLoading from '../../hooks/use-infinite-loading';

export default function() {
    const [filterParams, setFilterParams] = useState({});

    // 处理筛选和排序变化
    const handleFilterChange = (params) => {
        setFilterParams(params);
    };

    // 处理页面点击事件，用于关闭下拉菜单
    const handlePageClick = () => {
        Taro.eventCenter.trigger('page:click');
    };

    // 构建 URL
    const url = queryString.stringifyUrl({
        url: 'hotel',
        query: filterParams
    });

    // 从 hotel 接口获取酒店数据
    const { data = [] } = useInfiniteLoading({
        url,
    });

    return <View className={'bg-white min-h-screen'} onClick={handlePageClick}>
        <Background>
            <NavBar>
                <PageTitle title={'住宿'} />
            </NavBar>
            <SearchBar placeholder={'搜索酒店'} type="hotel" />
            <FilterSortBar
                onFilterChange={handleFilterChange}
                sortOptions={[
                    { field: 'rating', label: '评分', mode: SortMode.DESC },
                    { field: 'price', label: '价格', mode: SortMode.BOTH }
                ]}
            />
        </Background>
        <View className={'bg-[#F6F6F6] h-[8px]'} />
        <ScrollView>
            <ListView padding={[15, 15, 15, 15]}>
                {data.map((item) => {
                    return <HotelItem key={item.id} hotel={item} />;
                })}
            </ListView>
        </ScrollView>
        <SafeArea position={'bottom'} />
    </View>;
}
