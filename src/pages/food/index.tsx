import { Empty, SafeArea } from '@nutui/nutui-react-taro';
import { Image, Navigator, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import useRequest from '../../hooks/use-request';
import getImageUrl from '../../utils/get-image-url';
import request from '../../utils/request';

export default function() {
    const { result: foods = [], refresh } = useRequest({
        url: 'eatery/food'
    });

    return <View>
        <Background white>
            <NavBar>
                <PageTitle title={'商品管理'} />
            </NavBar>
        </Background>
        <View className={'p-[12px] pb-[70px]'}>
            <View className={'flex flex-col gap-[12px]'}>
                {foods.length == 0 && <Empty title='暂无商品' />}
                {foods.map((food) => {
                    return <View className={'p-[8px] rounded-[12px] bg-white flex items-center justify-between'}>
                        <View className={'flex gap-[8px] items-stretch'}>
                            <Image className={'size-[76px] rounded-[8px]'} src={getImageUrl(food.cover)} />
                            <View className={'flex flex-col py-[4px] justify-between'}>
                                <View className={'text-[15px]'}>{food.name}</View>
                                <View className={'text-[13px] text-[#FF724C]'}>￥<Text className={'text-[17px]'}>{food.price}</Text></View>
                            </View>
                        </View>
                        <View className={'flex items-center gap-[8px]'}>
                            <View
                                className={'rounded-[50%] bg-[#F6F6F6] size-[36px] flex items-center justify-center'}
                                onClick={() => {
                                    Taro.navigateTo({
                                        url: `/pages/food/edit?id=${food.id}`
                                    });
                                }}
                            >
                                <Image className={'size-[20px]'} src={require('../../assets/edit-dark.svg')} />
                            </View>
                            <View onClick={async () => {
                                const { confirm } = await Taro.showModal({
                                    title: '确定要删除吗？',
                                });
                                if (confirm) {
                                    await request({
                                        url: `eatery/food/${food.id}`,
                                        method: 'DELETE',
                                        toast: true
                                    });
                                    await refresh();
                                }
                            }} className={'rounded-[50%] bg-[#F6F6F6] size-[36px] flex items-center justify-center'}>
                                <Image className={'size-[20px]'} src={require('../../assets/delete.svg')} />
                            </View>
                        </View>
                    </View>;
                })}
            </View>
            <View className={'fixed bottom-0 left-0 right-0 bg-white'}>
                <View className={'px-3 py-2 flex'}>
                    <Navigator url={'/pages/food/create'} className={'p-[10px] grow text-center rounded-3xl bg-[#58B4BF] text-white text-[17px] font-semibold'}>添加商品</Navigator>
                </View>
                <SafeArea position={'bottom'} />
            </View>
        </View>
        <SafeArea position={'bottom'} />
    </View>;
}
