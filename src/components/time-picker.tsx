import { DatePicker, DatePickerProps, PickerActions } from '@nutui/nutui-react-taro';
import { View, Text } from '@tarojs/components';
import { useRef } from 'react';

interface Props extends Partial<Omit<DatePickerProps, 'value' | 'onChange'>> {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export default function({ placeholder, value, onChange, ...props }: Props) {
  const ref = useRef<PickerActions>(null);
  return <DatePicker
    {...props}
    value={parseTime(value)}
    onConfirm={(_, selectedValue) => {
      onChange?.(selectedValue.join(':'));
    }}
    ref={ref}
  >{() => {
    return <View onClick={() => {
      ref.current?.open();
    }}>{value || <Text className={'text-[#757575]'}>{placeholder}</Text>}</View>;
  }}</DatePicker>;
}

function parseTime(time?: string) {
  if (!time) {
    return undefined;
  }
  const date = new Date();
  const dateString = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}:00`;

  return new Date(`${dateString} ${time}`);
}
