import { Checkbox, SafeArea } from '@nutui/nutui-react-taro';
import { Image, View, Text, Button } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import Background from '../../components/background';
import NavBar from '../../components/nav-bar';
import PageTitle from '../../components/page-title';
import { useCallback, useState } from 'react';
import request from '../../utils/request';

export default function Index() {
    const { params: { redirect } } = useRouter();
    const [agreementChecked, setAgreementChecked] = useState(false);

    const handleRegister = useCallback(async ({ detail }) => {
        if (detail.code) {
            Taro.showLoading();
            try {
                const result = await Taro.login();

                const user = await request({
                    url: `/auth/register`,
                    data: {
                        mobile: detail.code,
                        code: result.code,
                    },
                    method: 'POST'
                });

                Taro.setStorageSync('user', user);
                if (redirect) {
                    Taro.redirectTo({
                        url: decodeURIComponent(redirect)
                    });
                } else {
                    Taro.navigateBack();
                }
            } finally {
                Taro.hideLoading();
            }
        }
    }, [redirect]);

    const handleCancel = () => {
        Taro.navigateBack();
    };

    return <View className={'bg-white min-h-screen'}>
        <Background>
            <NavBar>
                <PageTitle />
            </NavBar>
        </Background>
        <View className={'flex justify-center items-center mt-20'}>
            <Image className={'w-[131px] h-[119px]'} src={require('../../assets/logo.png')} />
        </View>
        <View className={'absolute bottom-0 left-0 right-0 p-[25px]'}>
            <View className={'mb-[16px] flex items-center justify-center gap-[2px]'}>
                <Checkbox
                    checked={agreementChecked}
                    onChange={(val) => setAgreementChecked(val)}
                    style={{
                        // @ts-ignore
                        '--nut-icon-width': '15px',
                        '--nut-icon-height': '15px'
                    }}>
                    <Text className={'text-[13px] text-[#666666]'}>我已阅读并同意</Text>
                </Checkbox>
                <Text className={'text-[13px] text-[#58B4BF]'} onClick={() => {
                    Taro.navigateTo({
                        url: '/pages/login/agreement'
                    });
                }}>《吴忠早茶用户协议》</Text>
            </View>
            {agreementChecked ? (
                <Button
                    onGetPhoneNumber={handleRegister}
                    openType='getPhoneNumber'
                    className={`p-[13px] rounded-[10px] text-center text-[16px] leading-none bg-[#58B4BF] text-white`}>
                    授权手机号登录
                </Button>
            ) : (
                <Button
                    onClick={() => {
                        Taro.showToast({
                            title: '请先阅读并同意用户协议',
                            icon: 'none'
                        });
                    }}
                    className={`p-[13px] rounded-[10px] text-center text-[16px] leading-none bg-[#E5E5E5] text-[#999999]`}>
                    授权手机号登录
                </Button>
            )}
            <View className={'text-center mt-[16px]'}><Text onClick={handleCancel} className={'text-[#58B4BF] text-[15px]'}>暂不登录</Text></View>
            <SafeArea position={'bottom'} />
        </View>
    </View>;
}
